from abc import ABC, abstractmethod
from typing import override

import pandas as pd
from pydantic import BaseModel

from src.service.http_service import HttpService
from src.util.pydantic_util import from_dict_array


class G3AnyQueryRequest(ABC):

    @abstractmethod
    def get_query_params(self) -> dict[str, str]:
        pass

    @abstractmethod
    def get_query(self) -> str:
        pass

    @abstractmethod
    def get_columns(self) -> list[str]:
        pass


class DeltaLrvPaceRequest(G3AnyQueryRequest):

    def __init__(self, start_date, end_date):
        self.start_date = start_date
        self.end_date = end_date

    @override
    def get_query_params(self) -> dict[str, str]:
        return {
            'startDate': self.start_date,
            'endDate': self.end_date
        }

    @override
    def get_query(self) -> str:
        return r'''select a.Decision_ID,  ac.accom_class_id,  Occupancy_DT,  datediff(day, a.Business_DT, Occupancy_DT) as leadTime,  Delta_Value, lrv as lrv, ceiling_value as ceilingValue    from pace_lrv l      join (select cast(d.Caught_up_DTTM as date) as Business_DT, min(plrv.decision_id) as decision_id      from Pace_LRV pLrv      join decision d on d.Decision_ID = pLrv.Decision_ID      where cast(d.Caught_up_DTTM as date) between '{startDate}' and '{endDate}'  and pLrv.delta_value >= 0    group by cast(d.Caught_up_DTTM as date)) as a on a.decision_id = l.decision_id      join accom_class ac on ac.accom_class_id = l.accom_class_id and ac.status_id = 1   where l.delta_value >= 0   order by pace_lrv_id '''.format(
            **self.get_query_params())

    @override
    def get_columns(self) -> list[str]:
        return ['decisionId', 'accomClassId', 'occupancyDate', 'leadTime', 'deltaLrv', 'lrv', 'ceilingValue']

class MaxLrvDecisionOccDate(G3AnyQueryRequest):

    def __init__(self, caught_up_date):
        self.caught_up_date = caught_up_date

    @override
    def get_query_params(self) -> dict[str, str]:
        return {
            'caughtUpDate': self.caught_up_date
        }

    @override
    def get_query(self) -> str:
        return r'''select top 1 Decision_ID, max(Occupancy_DT) as maxOccDate from Decision_LRV    where Occupancy_DT >= '{caughtUpDate}'    group by Decision_ID order by Decision_ID desc  '''.format(
            **self.get_query_params())

    @override
    def get_columns(self) -> list[str]:
        return ['decisionId', 'maxOccDate']



class AccomAvailableCapacityRequest(G3AnyQueryRequest):

    def __init__(self, caught_up_date):
        self.caught_up_date = caught_up_date

    @override
    def get_query_params(self) -> dict[str, str]:
        return {
            'caughtUpDate': self.caught_up_date
        }

    @override
    def get_query(self) -> str:
        return r'''select Occupancy_DT,           at.Accom_Class_ID,           iif(sum(Accom_Capacity - Rooms_Sold - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other) < 0, 0,               sum(Accom_Capacity - Rooms_Sold - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other)) as availableCapacity, datediff(day, '{caughtUpDate}', Occupancy_DT) as leadTime , '{caughtUpDate}' as captureDate   from Accom_Activity aa             join accom_type at on at.Accom_Type_ID = aa.Accom_Type_ID    where Occupancy_DT between dateadd(day, -2, '{caughtUpDate}') and dateadd(day, 450, '{caughtUpDate}')    group by Occupancy_DT, at.Accom_Class_ID  '''.format(
            **self.get_query_params())

    @override
    def get_columns(self) -> list[str]:
        return ['occupancyDate', 'accomClassId', 'availableCapacity', 'leadTime', 'captureDate']

class CdpScheduleRequest(G3AnyQueryRequest):

    @override
    def get_query_params(self) -> dict[str, str]:
        pass

    @override
    def get_query(self) -> str:
        return (r'''SELECT * FROM CDP_SCHEDULE;''')

    @override
    def get_columns(self) -> list[str]:
        return ['cdpScheduleId', 'cdpTime', 'lastUpdatedByUserId', 'lastUpdatedDTTM', 'createdByUserId', 'createdDTTM', 'lastRunDTTM']


class G3AnyApiService:
    ANY_API_PATH = 'api/G3Tables/executeQuery/v1'

    def __init__(self, http_service: HttpService, property_id):
        self.http_service = http_service
        self.property_id_param = {
            'propertyId': property_id
        }

    def fetch(self, request: G3AnyQueryRequest) -> pd.DataFrame:
        res = self.http_service.post(self.ANY_API_PATH, body=self.get_body(request.get_query()),
                                     params=self.property_id_param, headers={'Content-Type': 'application/json'})
        return pd.DataFrame(data=res, columns=request.get_columns())

    def fetch_as_type(self, request: G3AnyQueryRequest, return_type: list[BaseModel]):
        df: pd.DataFrame = self.fetch(request)
        return from_dict_array(return_type, df.to_dict(orient='records'))

    @staticmethod
    def get_body(query: str) -> dict[str, str]:
        return {'query': query}
