from enum import Enum


class EnvironmentVariable(Enum):
    AVAILABLE_CAPACITY_TABLE = 'AVAILABLE_CAPACITY_TABLE'
    DECISION_CHANGE_TABLE = 'DECISION_CHANGE_TABLE'
    USE_G3_ANY_API = 'USE_G3_ANY_API'
    INTEGRATION_SETTING_URL = "INTEGRATION_SETTING_URL"
    DELTA_LRV_LATEST_TABLE = "DELTA_LRV_LATEST_TABLE"
    REF_PRICE_LATEST_TABLE = "REF_PRICE_LATEST_TABLE"
    LOG_LEVEL = 'LOG_LEVEL'
    AWS_SNS_ENDPOINT_URL = 'AWS_SNS_ENDPOINT_URL'
    ENV = 'ENV'
    FDS_SNS_TOPIC_ARN = 'FDS_SNS_TOPIC_ARN'
    ACCOM_TYPE_MAPPING_DB = 'ACCOM_TYPE_MAPPING_DB'
    S3_NAME = 'S3_NAME'
    AWS_REGION = 'AWS_REGION'
    DEFAULT_PAGE_SIZE = 'DEFAULT_PAGE_SIZE'
    G3_AUTH_TOKEN = 'G3_AUTH_TOKEN'
    G3_AUTH_SECRET_NAME = 'G3_AUTH_SECRET_NAME'
    NO_OF_PAST_BDE_TO_CONSIDER = 'NO_OF_PAST_BDE_TO_CONSIDER'
    PROPERTY_INC_SOLDS_TABLE = 'PROPERTY_INC_SOLDS_TABLE'
    CALIBRATED_POTENTIAL_TABLE = 'CALIBRATED_POTENTIAL_TABLE'
    IDP_COUNT_TABLE = 'IDP_COUNT_TABLE'
    IDP_WINDOW_TABLE = 'IDP_WINDOW_TABLE'
    IDP_WINDOW_BY_ROOMCLASS_TABLE = 'IDP_WINDOW_BY_ROOMCLASS_TABLE'
    CALIB_DATE_TABLE = 'CALIB_DATE_TABLE'
    SAVE_INC_SOLDS = "SAVE_INC_SOLDS"
    LAST_INCREMENTAL_TIME_TABLE = "LAST_INCREMENTAL_TIME_TABLE"
    UCS_BASE_URL = "UCS_BASE_URL"
    UIS_BASE_URL = "UIS_BASE_URL"
    FDS_UCS_TOKEN = "FDS_UCS_TOKEN"
    G3_BASE_URL = "G3_BASE_URL"
