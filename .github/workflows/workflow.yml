# The name of our workflow, it'll be displayed in workflows section on repository's actions page.
name: "Build & Deploy"

permissions:
  contents: read
  id-token: write
  checks: write
  pull-requests: write
  deployments: read

on:
  push:
    branches:
      - 'main'
  pull_request:
  workflow_dispatch:

jobs:
  # Here we set workflow's environment vars.
  ## -------------------------------------------------------------------------------------------------------------------
  # Unfortunately we can't use ENV in reusable workflows like:
  # uses: ./.github/workflows/reusable_build.yml
  #    with:
  #      aws_account_id: ${{ env.AWS_ACCOUNT_ID_DEV }}
  # This is a GitHub limitation we should deal with right now
  # Here are some links that might be updated in the future with more elegant solutions:
  # https://github.community/t/passing-environment-variables-to-reusable-workflow/230456/5
  # https://github.com/actions/runner/issues/480
  ## -------------------------------------------------------------------------------------------------------------------
  params:
    name: 'Set Environment Variables'
    runs-on: 'ubuntu-latest'
    outputs:
      params: ${{ steps.env-vars.outputs.env_vars }}
    steps:
      - id: env-vars
        uses: ideasorg/ideas-github-set-env-action@v0

  verify:
    if: github.event_name == 'pull_request'
    needs:
      - params
    uses: ideasorg/ideas-github-workflows/.github/workflows/lints_and_checks.yml@v0
    with:
      tf_precommit: ${{ fromJSON(needs.params.outputs.params).TF_FMT_AND_DOCS }}
      tf_validate: ${{ fromJSON(needs.params.outputs.params).TF_VALIDATE }}
      tf_lint: ${{ fromJSON(needs.params.outputs.params).TFLint }}
      tf_sec: ${{ fromJSON(needs.params.outputs.params).TFSec }}
      tf_dir: ${{ fromJSON(needs.params.outputs.params).TF_DIR }}
    secrets: inherit

  PYTHON_BUILD:
    needs:
      - params
    uses: ideasorg/ideas-github-workflows/.github/workflows/python_lambda_build.yml@v0
    with:
      lambdas: ${{ fromJSON(needs.params.outputs.params).PYTHON_LAMBDAS_DIRS }}
      lambda_dir: ${{ fromJSON(needs.params.outputs.params).LAMBDA_ROOT_DIR }}
      poetry_tests: "false" # don't run tests on `main`
      sonar_run: false # don't run sonar on `main`
      poetry_lint: ${{ !fromJSON(needs.params.outputs.params).MAIN_BRANCH }} # don't run lint on `main`
      lint_args: 'src tests'
      only_production_no_tests: ${{ fromJSON(needs.params.outputs.params).MAIN_BRANCH }} # install only production dependencies  on `main` branch
      python_version: '3.12.0'
      poetry_version: '2.1.1'
    secrets: inherit

  # This job performs terraform plan and apply for the dev environment via 'terraform reusable workflow', you can see detailed steps in the below specified path.
  terraform_dev:
    if: |
      !failure() &&
      !cancelled()
    needs:
      - params
      - PYTHON_BUILD
      - verify
    uses: ideasorg/ideas-github-workflows/.github/workflows/pure_terraform.yml@v0
    # In this section via "with" we can parametrize our workflow, and set necessary variables.
    # For more details check the README https://github.com/ideasorg/ideas-github-workflows/blob/main/.github/workflows/PURE_TERRAFORM_README.md
    with:
      tf_state_name: ${{ fromJSON(needs.params.outputs.params).TF_STATE_NAME }}
      aws_account_id: ${{ fromJSON(needs.params.outputs.params).AWS_ACCOUNT_ID_DEV }}
      tf_dir: ${{ fromJSON(needs.params.outputs.params).TF_DIR }}
      tf_plan_args: '-var-file=env/dev.tfvars'
      repo_environment: 'dev'
      skip_checks: true
      download_external_artifact: 'python'
    secrets: inherit

  clean_up_lambdas_dev:
    if: |
      !failure() &&
      !cancelled()
    needs:
      - terraform_dev
      - params
    steps:
      - name: "Clean up"
        uses: ideasorg/ideas-github-old-lambdas-cleanup@v0.6.1
        with:
          aws_account_id: ${{ fromJSON(needs.params.outputs.params).AWS_ACCOUNT_ID_DEV }}
          lambdas: ${{ fromJSON(needs.params.outputs.params).LAMBDAS_NAMES }}
          versions_to_keep: ${{ fromJSON(needs.params.outputs.params).VERSIONS_TO_KEEP }}
    runs-on: ubuntu-latest

  # This job performs the same things for the stage environment, check the terraform_dev job's notes.
  terraform_stage:
    needs:
      - params
      - terraform_dev
    # This if controls when the terraform_stage job can run, it'll be run only if merge performs to main branch.
    if: |
      !failure() &&
      !cancelled() &&
      github.ref == 'refs/heads/main'
    uses: ideasorg/ideas-github-workflows/.github/workflows/pure_terraform.yml@v0
    with:
      tf_state_name: ${{ fromJSON(needs.params.outputs.params).TF_STATE_NAME }}
      aws_account_id: ${{ fromJSON(needs.params.outputs.params).AWS_ACCOUNT_ID_STAGE }}
      tf_dir: ${{ fromJSON(needs.params.outputs.params).TF_DIR }}
      tf_plan_args: '-var-file=env/stage.tfvars'
      repo_environment: 'stage'
      skip_checks: true
      download_external_artifact: 'python'
    secrets: inherit


  clean_up_lambdas_stage:
    if: |
      !failure() &&
      !cancelled() &&
      github.ref == 'refs/heads/main'
    needs:
      - terraform_stage
      - params
    steps:
      - name: "Clean up"
        uses: ideasorg/ideas-github-old-lambdas-cleanup@v0.6.1
        with:
          aws_account_id: ${{ fromJSON(needs.params.outputs.params).AWS_ACCOUNT_ID_STAGE }}
          lambdas: ${{ fromJSON(needs.params.outputs.params).LAMBDAS_NAMES }}
          versions_to_keep: ${{ fromJSON(needs.params.outputs.params).VERSIONS_TO_KEEP }}
    runs-on: ubuntu-latest

  # This job performs the same things for the prod environment, check the terraform_dev job's notes.
  terraform_prod:
    needs:
      - params
      - terraform_stage
    # This if controls when the terraform_stage job can run, it'll be run only if merge performs to main branch.
    if: |
      !failure() &&
      !cancelled() &&
      github.ref == 'refs/heads/main'
    uses: ideasorg/ideas-github-workflows/.github/workflows/pure_terraform.yml@v0
    with:
      tf_state_name: ${{ fromJSON(needs.params.outputs.params).TF_STATE_NAME }}
      aws_account_id: ${{ fromJSON(needs.params.outputs.params).AWS_ACCOUNT_ID_PROD }}
      tf_dir: ${{ fromJSON(needs.params.outputs.params).TF_DIR }}
      tf_plan_args: '-var-file=env/prod.tfvars'
      repo_environment: 'prod'
      skip_checks: true
      download_external_artifact: 'python'
    secrets: inherit

  clean_up_lambdas_prod:
    if: |
      !failure() &&
      !cancelled() &&
      github.ref == 'refs/heads/main'
    needs:
      - terraform_prod
      - params
    steps:
      - name: "Clean up"
        uses: ideasorg/ideas-github-old-lambdas-cleanup@v0.6.1
        with:
          aws_account_id: ${{ fromJSON(needs.params.outputs.params).AWS_ACCOUNT_ID_PROD }}
          lambdas: ${{ fromJSON(needs.params.outputs.params).LAMBDAS_NAMES }}
          versions_to_keep: ${{ fromJSON(needs.params.outputs.params).VERSIONS_TO_KEEP }}
    runs-on: ubuntu-latest