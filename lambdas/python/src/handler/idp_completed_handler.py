import logging
from datetime import date, datetime
from typing import override

from src.abc.event_handler import EventHandler
from src.events.idp_completed_event import IDPCompletedEvent
from src.log_config.setup import setup_logger
from src.service.data_refresh_service import DataRefreshServiceFactory
from src.service.dynamodb_service import dynamodb_service
from src.service.integration_setting_service import IntegrationSettingService


class IDPCompletedHandler(EventHandler):
    logger = logging.getLogger(__name__)

    @override
    def handle(self, event: IDPCompletedEvent):
        assert isinstance(event, IDPCompletedEvent), "event is not IDP Completed"
        setting_service: IntegrationSettingService = IntegrationSettingService.from_int_set_url()
        if not setting_service.is_dynamic_optimization_enabled(event.client_code, event.property_code):
            self.logger.debug("Skipping BDE completed event")
            return
        dynamodb_service.update_last_optimization_time(client_code=event.client_code, property_code=event.property_code)
        data_refresh_service = DataRefreshServiceFactory.from_client_property_codes_base_url(event.client_code,
                                                                                             event.property_code,
                                                                                             event.base_callback_u_r_l,
                                                                                             event.property_id)
        data_refresh_service.refresh_evaluation_data_store_lrv_change(event.caught_up_date, event.client_code,
                                                                      event.property_code)


if __name__ == '__main__':
    setup_logger()
    IDPCompletedHandler().handle(IDPCompletedEvent(base_callback_u_r_l='http://mn4qg3xenvl004.ideasdev.int',
                                                   client_code='Hilton', property_code='REKCU', property_id=993090,
                                                   caught_up_date=date.fromisoformat('2024-11-18')))
