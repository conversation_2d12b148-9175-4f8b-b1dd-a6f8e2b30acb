import logging
from datetime import date, datetime
from typing import override

from src.abc.event_handler import <PERSON>Handler
from src.events.idp_triggered_event import IDPTriggeredEvent
from src.log_config.setup import setup_logger
from src.service.data_refresh_service import DataRefreshServiceFactory
from src.service.dynamodb_service import dynamodb_service
from src.service.integration_setting_service import IntegrationSettingService


class IDPTriggeredHandler(EventHandler):
    logger = logging.getLogger(__name__)

    @override
    def handle(self, event: IDPTriggeredEvent):
        assert isinstance(event, IDPTriggeredEvent), "event is not IDP Triggered"
        setting_service: IntegrationSettingService = IntegrationSettingService.from_int_set_url()
        if not setting_service.is_dynamic_optimization_enabled(event.client_code, event.property_code):
            self.logger.debug("Skipping IDP Triggered Event")
            return
        dynamodb_service.increment_idp_count(client_code=event.client_code, property_code=event.property_code, caught_up_date=event.caught_up_date)
        data_refresh_service = DataRefreshServiceFactory.from_client_property_codes_base_url(event.client_code,
                                                                                             event.property_code,
                                                                                             event.base_callback_u_r_l,
                                                                                             event.property_id)
        data_refresh_service.refresh_evaluation_data_store_lrv_change(event.caught_up_date, event.client_code,
                                                                      event.property_code)
        self.logger.info("Successfully Incremented IDP count after IDP was triggered")


if __name__ == '__main__':
    setup_logger()
