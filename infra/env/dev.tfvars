environment = "dev"
fds_uen_arn = "arn:aws:sns:us-east-2:094974311374:fds-unified-event-notification-topic"

us_east_2_vpc_id = "vpc-086c69276f8630722"

us_east_2a_private_subnet_id = "subnet-076280e5b4c1540e5"
us_east_2b_private_subnet_id = "subnet-07e3fb8093ba53efd"
us_east_2c_private_subnet_id = "subnet-0af454db20fd90379"

integration_setting_url = "https://integration-setting-internal.dev.ideasrms.com"


eval_time_interval = 720

secret_manager_name = "dyn-opt-secret"

uis_base_url = "https://fds.stage.ideasrms.com/api/uis"
ucs_base_url = "https://fds.stage.ideasrms.com/api/ucs"
g3_base_url  = "https://mn4qg3xenvl004.ideasdev.int"