from requests import Request
from requests.auth import AuthBase

from src.enums.env_var import EnvironmentVariable
from src.util.env_service import get_value_or_default
from src.util.secret_manager import get_g3_auth_secret


class G3Auth(AuthBase):
    def __call__(self, request: Request):
        auth_token = get_value_or_default(EnvironmentVariable.G3_AUTH_TOKEN, get_g3_auth_secret)
        request.headers['Authorization'] = f'Basic {auth_token}'
        return request
