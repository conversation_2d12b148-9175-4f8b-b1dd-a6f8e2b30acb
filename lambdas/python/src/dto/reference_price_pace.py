from datetime import date, timedelta
from decimal import Decimal

from pydantic import BaseModel, ConfigDict, computed_field
from pydantic.alias_generators import to_camel


class ReferencePricePace(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    accom_class_id: int
    occupancy_date: date
    lead_time: int
    price: Decimal

    @computed_field
    @property
    def capture_date(self) -> date:
        return self.occupancy_date - timedelta(days=self.lead_time)

    def dynamodb_record(self, client_code, property_code):
        return {
            'clientCode_propertyCode': self.get_partition_key(client_code, property_code),
            'clientCode': client_code,
            'propertyCode': property_code,
            'accomClassId': self.accom_class_id,
            'occupancyDate': self.occupancy_date.isoformat(),
            'leadTime': self.lead_time,
            'price': self.price,
            'captureDate': self.capture_date.isoformat(),
            'accomClassId_occupancyDate': f'{self.accom_class_id}_{self.occupancy_date.isoformat()}'
        }

    @staticmethod
    def get_partition_key(client_code, property_code):
        return f'{client_code}_{property_code}'
