from enum import Enum
from typing import Callable, Union

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

from src.events.accom_class_mapping_changed_event import AccomClassMappingChangedEvent
from src.events.activity_completed_bde_event import ActivityCompletedBDEEvent
from src.events.activity_completed_idp_event import ActivityCompletedIDPEvent
from src.events.bde_completed_event import BDECompletedEvent
from src.events.ft_ac_report_ready_event import FTACReportReadyEvent
from src.events.idp_completed_event import IDPCompletedEvent
from src.events.idp_triggered_event import IDPTriggeredEvent
from src.events.potential_calibration_event import PotentialCalibrationEvent
from src.events.significant_solds_drift_detected_event import SignificantSoldsDriftDetectedEvent
from src.events.solds_drift_detected_event import SoldsDriftDetectedEvent
from src.events.trigger_evaluation_event import TriggerEvaluationEvent
from src.handler.accom_type_accom_class_mapping_change import AccomTypeAccomClassMappingChange
from src.handler.activity_completed_handler import ActivityCompletedHandler
from src.handler.bde_completed_handler import BDECompletedHandler
from src.handler.evaluation_check_handler import EvaluationCheckHandler
from src.handler.ft_ac_report_ready_handler import FTACReportReadyHandler
from src.handler.idp_completed_handler import IDPCompletedHandler
from src.handler.idp_triggered_handler import IDPTriggeredHandler
from src.handler.potential_calibration_handler import PotentialCalibrationHandler
from src.handler.significant_solds_drift_detected_handler import SignificantSoldsDriftHandler
from src.handler.solds_drift_handler import SoldsDriftHandler

SupportedEvents = Union[
    AccomClassMappingChangedEvent, FTACReportReadyEvent, PotentialCalibrationEvent, TriggerEvaluationEvent, SoldsDriftDetectedEvent, BDECompletedEvent, \
        SignificantSoldsDriftDetectedEvent, ActivityCompletedBDEEvent, ActivityCompletedIDPEvent, IDPCompletedEvent, IDPTriggeredEvent]


class EventTypeDetail(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    event_name: str
    handler: Callable[[SupportedEvents], None]
    event_type: type[SupportedEvents]


class EventType(Enum):
    ACCOMODATION_CONFIG_CHANGE = EventTypeDetail(event_name='ACCOMODATION_CONFIG_CHANGE',
                                                 handler=AccomTypeAccomClassMappingChange().handle,
                                                 event_type=AccomClassMappingChangedEvent)
    FORECAST_GROUP_CONFIG_CHANGED = EventTypeDetail(event_name='FORECAST_GROUP_CONFIG_CHANGED',
                                                    handler=AccomTypeAccomClassMappingChange().handle,
                                                    event_type=AccomClassMappingChangedEvent)
    FT_AC_REPORT_GENERATED = EventTypeDetail(event_name='FT_AC_REPORT_GENERATED',
                                             handler=FTACReportReadyHandler().handle, event_type=FTACReportReadyEvent)
    PotentialCalibrationEvent = EventTypeDetail(event_name='PotentialCalibrationEvent',
                                                handler=PotentialCalibrationHandler().handle,
                                                event_type=PotentialCalibrationEvent)
    SOLDS_DRIFT_DETECTED = EventTypeDetail(event_name='SOLDS_DRIFT_DETECTED', handler=SoldsDriftHandler().handle,
                                           event_type=SoldsDriftDetectedEvent)
    BDE_COMPLETED = EventTypeDetail(event_name='BDE_COMPLETED', handler=BDECompletedHandler().handle,
                                    event_type=BDECompletedEvent)
    IDP_COMPLETED = EventTypeDetail(event_name='IDP_COMPLETED', handler=IDPCompletedHandler().handle,
                                    event_type=IDPCompletedEvent)
    SIGNIFICANT_SOLDS_DRIFT_DETECTED = EventTypeDetail(event_name='SIGNIFICANT_SOLDS_DRIFT_DETECTED',
                                                       handler=SignificantSoldsDriftHandler().handle,
                                                       event_type=SignificantSoldsDriftDetectedEvent)
    TRIGGER_EVALUATION = EventTypeDetail(event_name='TRIGGER_EVALUATION', handler=EvaluationCheckHandler().handle,
                                         event_type=TriggerEvaluationEvent)
    ACTIVITY_COMPLETED_BDE = EventTypeDetail(event_name='ACTIVITY_COMPLETED_BDE',
                                             handler=ActivityCompletedHandler().handle,
                                             event_type=ActivityCompletedBDEEvent)
    ACTIVITY_COMPLETED_IDP = EventTypeDetail(event_name='ACTIVITY_COMPLETED_IDP',
                                             handler=ActivityCompletedHandler().handle,
                                             event_type=ActivityCompletedIDPEvent)
    IDP_TRIGGERED = EventTypeDetail(event_name='IDP_TRIGGERED', handler=IDPTriggeredHandler().handle,
                                   event_type=IDPTriggeredEvent)
