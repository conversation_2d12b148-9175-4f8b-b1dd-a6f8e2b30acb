import datetime
from unittest.mock import patch, MagicMock, call

from src.dto.accom_class import AccomClass
from src.dto.accom_type import AccomType
from src.events.accom_class_mapping_changed_event import AccomClassMappingChangedEvent
from src.handler.accom_type_accom_class_mapping_change import AccomTypeAccomClassMappingChange
from src.service.data_refresh_service import DataRefreshService
from src.service.g3_data_pull_service import G3DataPullService


class TestAccomTypeAccomClassMappingChangeHandler:
    handler = AccomTypeAccomClassMappingChange()

    @patch('src.service.data_refresh_service.DataRefreshServiceFactory.from_services')
    @patch('src.service.g3_data_pull_service.G3DataPullServiceFactory.init')
    @patch('src.service.dynamodb_service.DynamoDBService.update_accom_class_mapping')
    def test_handle(self, dynamodb: <PERSON>Mock, g3_data_pull_factory: MagicMock, data_refresh_factory: MagicMock) -> None:
        dynamodb.return_value = None
        g3_data_pull_mock = MagicMock(autospec=G3DataPullService)
        g3_data_pull_factory.return_value = g3_data_pull_mock
        data_refresh_service = MagicMock(autospec=DataRefreshService)
        data_refresh_factory.return_value = data_refresh_service
        data_refresh_service.refresh_calibration_data.return_value = None

        accom_types = [AccomType(id=1, name=str(1), accom_type_code=str(1),
                                 accom_class=AccomClass(id=1, code=str(1), name=str(1)),
                                 status_id=1, system_default=0)]
        g3_data_pull_mock.fetch_accom_types.return_value = accom_types
        self.handler.handle(
            AccomClassMappingChangedEvent(base_callback_u_r_l='url', property_id=1, property_code='c', client_code='c',
                                          caught_up_date=datetime.date.today()))

        assert dynamodb.call_args == call(accom_class_mapping=accom_types, client_code='c', property_code='c')
        assert g3_data_pull_mock.fetch_accom_types.call_args == call()
        assert data_refresh_service.refresh_calibration_data.call_args_list == [call(client_code='c', property_code='c',
                                                                                     caught_up_date=datetime.date.today(), property_id=1,
                                                                                     g3_base_call_url='url')]
