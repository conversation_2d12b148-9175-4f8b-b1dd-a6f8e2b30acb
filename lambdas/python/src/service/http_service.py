import csv
import json
import io

import requests
from requests import Response

from src.util.perf import timeit
from src.util.pydantic_util import from_dict_array


class HttpException(Exception):
    pass


class HttpService:

    def __init__(self, base_url: str, authorizer=None):
        self.base_url = base_url
        self.authorizer = authorizer

    def __get_full_url(self, url: str):
        return f'{self.base_url}/{url}'

    def get(self, path: str, return_type, params=None, **kwargs):
        """kwargs which `requests.get` accepts"""

        full_url = self.__get_full_url(path)
        res = requests.get(full_url, auth=self.authorizer, params=params, **kwargs)

        if not res.ok:
            raise HttpException(f"Request to {full_url} failed with status code {res.status_code} for1 {res.reason}")

        content_type = res.headers.get('Content-Type', '').lower()
        if 'text/csv' in content_type or 'application/csv' in content_type:
            return self._handle_csv(res, return_type)

        return from_dict_array(return_type, res.json())

    def get_raw(self, path: str, params=None, **kwargs):
        """kwargs which `requests.get` accepts"""

        full_url = self.__get_full_url(path)
        res = requests.get(full_url, auth=self.authorizer, params=params, **kwargs)

        if not res.ok:
            raise HttpException(f"Request to {full_url} failed with status code {res.status_code} for1 {res.reason}")

        return res.json()

    def get_raw_response(self, path: str, params=None, **kwargs) -> Response:
        """kwargs which `requests.get` accepts"""

        full_url = self.__get_full_url(path)
        res = requests.get(full_url, auth=self.authorizer, params=params, **kwargs)
        if res.status_code == 401:
            self.refresh_token()
            res = requests.get(full_url, auth=self.authorizer, params=params, **kwargs)
        return res

    @timeit()
    def get_paged_api(self, path: str, return_type, page_size: int, params=None, page_num: int = 0, **kwargs):
        result = []
        if params is None:
            params = {}
        while True:
            params["page"] = page_num
            params["size"] = page_size
            cur_result = self.get(path, return_type, params, **kwargs)
            if not isinstance(cur_result, list) or cur_result is None:
                return cur_result
            if len(cur_result) < page_size:
                return result + cur_result
            page_num += page_size
            result += cur_result

    @staticmethod
    def download_file(full_path: str) -> requests.Response:
        res = requests.get(full_path)

        if not res.ok:
            raise HttpException(f"Request to {full_path} failed with status code {res.status_code} for1 {res.reason}")
        return res

    def post(self, path: str, body: dict, **kwargs):
        """kwargs which `requests.post` accepts"""
        full_url = self.__get_full_url(path)
        res = requests.post(full_url, data=json.dumps(body), auth=self.authorizer, **kwargs)
        if res.status_code == 401:
            self.refresh_token()
            res = requests.post(full_url, data=json.dumps(body), auth=self.authorizer, **kwargs)
        if not res.ok:
            raise HttpException(f"Request to {full_url} failed with status code {res.status_code} for1 {res.reason}")
        return res.json()

    def _handle_csv(self, res, return_type):
        """Parses CSV response into a list of dicts and converts them to the specified return type."""
        csv_data = io.StringIO(res.text)
        reader = csv.DictReader(csv_data)
        data_list = list(reader)
        return from_dict_array(return_type, data_list)

    def refresh_token(self):
        self.authorizer.refresh_token()

    def post_with_params(self, path: str, params=None, **kwargs):
        """kwargs which `requests.post` accepts"""
        full_url = self.__get_full_url(path)
        if params is None:
            params = {}
        res = requests.post(full_url, params=params, auth=self.authorizer, **kwargs)
        if res.status_code == 401:
            self.refresh_token()
            res = requests.post(full_url, params=params, auth=self.authorizer, **kwargs)
        if not res.ok:
            raise HttpException(f"Request to {full_url} failed with status code {res.status_code} for1 {res.reason}")