services:
  localstack:
    image: localstack/localstack:latest
    container_name: do_local_stack
    environment:
      - SERVICES=dynamodb, s3, sns
      - DEFAULT_REGION=us-east-2
      - PERSISTENCE=1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
    ports:
      - "4566:4566"
    volumes:
      - "./data:/var/lib/localstack"
      - ./initialize_resources.sh:/etc/localstack/init/ready.d/initialize_resources.sh
