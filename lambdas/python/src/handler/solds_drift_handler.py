import logging
from datetime import datetime, date
from typing import override

from src.abc.event_handler import <PERSON>Handler
from src.dto.inc_solds import IncrementalSolds
from src.events.solds_drift_detected_event import SoldsDriftDetectedEvent, Drift
from src.service.dynamodb_service import DynamoDBService


class SoldsDriftHandler(EventHandler):
    logger = logging.getLogger(__name__)

    @override
    def handle(self, event: SoldsDriftDetectedEvent):
        cur_time = datetime.now().isoformat()
        dynamodb_service = DynamoDBService()
        property_code = event.property_code
        client_code = event.client_code
        room_type = event.drift.room_type_code
        accom_class_id = dynamodb_service.fetch_accom_type_mapping(client_code, property_code)
        if len(accom_class_id) == 0:
            self.logger.info(f"Ignoring solds drift as there is no accom class mapping present {event}")
            return
        existing_inc_solds_by_date: dict[str, int] = {i.occupancy_date: i.inc_solds
                                                      for i in dynamodb_service.fetch_incremental_solds(client_code,
                                                                                                        property_code)}
        inc_solds = lambda occ_date, solds: IncrementalSolds(property_code=property_code, client_code=client_code,
                                                             accom_class_id=int(accom_class_id.get(room_type, -1)),
                                                             occupancy_date=str(occ_date), inc_solds=solds,
                                                             last_updated_time=str(cur_time))
        to_save = [inc_solds(occ_date, existing_inc_solds_by_date.get(str(occ_date), 0) + inc)
                   for occ_date, inc in event.drift.incrementalSoldsByOccupancyDate.items()]
        dynamodb_service.update_incremental_solds(to_save)
        dynamodb_service.update_property_last_incremental_time(client_code, property_code)


if __name__ == '__main__':
    SoldsDriftHandler().handle(SoldsDriftDetectedEvent(property_code='REKCU', client_code='H',
                                                       drift=Drift(incrementalSoldsByOccupancyDate={
                                                           date.fromisoformat('2024-01-01'): 1}, market_segment='1',
                                                                   rate_code='R', reservation_id='1',
                                                                   room_type_code='K1'),blocked=True ))
