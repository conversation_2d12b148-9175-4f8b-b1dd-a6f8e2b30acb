# AWS Accounts IDs per ENV
AWS_ACCOUNT_ID_DEV=************
AWS_ACCOUNT_ID_STAGE=************
AWS_ACCOUNT_ID_PROD=************

PYTHON_LAMBDAS_DIRS=["python"]
LAMBDA_ROOT_DIR=./lambdas
LAMBDAS_NAMES=dyn-opt_evaluation_check dyn-opt_solds_drift_handler dyn-opt_data_collector
VERSIONS_TO_KEEP=10

# Terraform related
TF_DIR=infra
TF_STATE_NAME=dyn-opt-lambdas

# Lints and checks
TF_FMT_AND_DOCS=true
TF_VALIDATE=true
TFLint=true
TFSec=true