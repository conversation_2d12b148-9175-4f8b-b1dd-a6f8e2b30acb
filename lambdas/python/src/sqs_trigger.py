from src.context_vars.property_context import property_context_var
from src.dto.property_context_info import PropertyContextInfo
from src.log_config.setup import setup_logger
setup_logger()

import json

from src.dto.sqs_event import SourceSNSEvent
from src.util.pydantic_util import from_dict
import logging


def lambda_handler(event, context):
    logger = logging.getLogger(__name__)
    logger.debug(f"Received event: {event}")
    try:
        for r in event['Records']:
            body = json.loads(r['body'])
            if 'Message' in body.keys():
                body = json.loads(body['Message'])
            body['eventSource'] = json.loads(body['eventSource'])
            source_sns_event: SourceSNSEvent = from_dict(SourceSNSEvent, body)
            property_context_var.set(PropertyContextInfo(client_code=source_sns_event.event_source.client_code,
                                                         property_code=source_sns_event.event_source.property_code))
            logger.info(f"Started Processing: {source_sns_event}")
            source_sns_event.get_handler()(source_sns_event.event_source)
            logger.info(f"Finished Processing: {source_sns_event}")
            property_context_var.set(PropertyContextInfo(client_code='DEFAULT_CODE', property_code='DEFAULT_CODE'))
    except Exception as e:
        logger.exception(f"Failed to execute message: {e}")
        property_context_var.set(PropertyContextInfo(client_code='DEFAULT_CODE', property_code='DEFAULT_CODE'))
        return {
            'status_code': 500,
            'body': str(e)
        }
