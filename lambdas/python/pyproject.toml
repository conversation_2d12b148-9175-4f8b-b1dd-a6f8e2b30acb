[tool.poetry]
name = "dynamic-optimization-lambdas"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.12.0"
pydantic = ">=2.0.3,<3.0.0"
psutil = "^6.1.1"

[tool.poetry.group.test.dependencies]
requests = "^2.31.0"
pytest = "^8.0.0"
pytest-cov = "^4.1.0"


[tool.poetry.group.layer.dependencies]
awswrangler = { extras = ["openpyxl"], version = "^3.5.2" }
aws-lambda-powertools = {extras = ["parser"], version = "^3.2.0"}


[tool.poetry.group.dev.dependencies]
pyxlsb = "^1.0.10"
pylint = "^3.1.0"


[tool.pytest.ini_options]
# `.` & 'tests' to include any util functions used only tests
# '../../.' to add root directory in pypath because usually we open root directory in intellij.
#    So all imports start from root dir..
pythonpath= ['.', 'tests', '../../.', 'src', 'resources']


[tool.poetry.scripts]
lint = "pylint.__init__:run_pylint"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
