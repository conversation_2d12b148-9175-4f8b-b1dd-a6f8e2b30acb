environment = "prod"
fds_uen_arn = "arn:aws:sns:us-east-2:569273926105:fds-unified-event-notification-topic"

us_east_2_vpc_id = "vpc-038d0349e924cc351"

us_east_2a_private_subnet_id = "subnet-0071c602872d540ff"
us_east_2b_private_subnet_id = "subnet-0d237293a88ed1937"
us_east_2c_private_subnet_id = "subnet-01b7538a730adf93c"

integration_setting_url = "https://integration-setting-internal.ideasrms.com"


eval_time_interval = 30

secret_manager_name = "rdl-analytic-secret"

uis_base_url = "https://fds.ideasrms.com/api/uis"
ucs_base_url = "https://fds.ideasrms.com/api/ucs"
g3_base_url  = "https://g3.ideas.com"
