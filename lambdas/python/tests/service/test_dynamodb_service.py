import unittest
from unittest.mock import patch, MagicMock, call

from src.dto.accom_class import AccomClass
from src.dto.accom_type import AccomType
from src.service.dynamodb_service import DynamoDBService


class TestDynamoDBService(unittest.TestCase):
    service = DynamoDBService()

    @patch('awswrangler.dynamodb.put_items')
    def test_update_accom_type_mapping(self, dynamodb_put_item_mock: MagicMock) -> None:
        dynamodb_put_item_mock.return_value = None
        accom_types = [
            AccomType(id=i, name=str(i), accom_type_code=str(i),
                      accom_class=AccomClass(id=i, code=str(i), name=str(i)), status_id=1, system_default=0)
            for i in range(0, 2)]
        self.service.update_accom_class_mapping(accom_class_mapping=accom_types, client_code='c', property_code='p')
        expected_saved_items = [{'clientCode': 'c',
                                 'propertyCode': 'p',
                                 'accomType': a.accom_type_code,
                                 'accomClassId': a.accom_class.id,
                                 'clientCode_propertyCode': 'c_p'} for a in accom_types]

        assert dynamodb_put_item_mock.call_args == call(items=expected_saved_items, table_name='do_table_name')
