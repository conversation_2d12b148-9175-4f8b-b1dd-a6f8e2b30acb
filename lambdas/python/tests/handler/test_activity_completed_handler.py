from datetime import datetime
from unittest.mock import patch, MagicMock
import pytest

from src.events.activity_completed_bde_event import ActivityCompletedBDEEvent
from src.events.activity_completed_idp_event import ActivityCompletedIDPEvent
from src.handler.activity_completed_handler import ActivityCompletedHandler
from src.service.dynamodb_service import DynamoDBService


class TestActivityCompletedHandler:
    MOCK_EVENT_BDE = ActivityCompletedBDEEvent(
        activity_completed=datetime.now(),
        activity_started=datetime.now(),
        corelation_id="abcd",
        fiscal_date=datetime.now(),
        input_type="BDE",
        integration_type="ACTIVITY_DATA",
        process_id="BDE",
        client_code="Hilton",
        property_code="ABILN",
        unified_property_id="12345"
    )

    MOCK_EVENT_IDP = ActivityCompletedIDPEvent(
        activity_completed=datetime.now(),
        activity_started=datetime.now(),
        corelation_id="efgh",
        fiscal_date=datetime.now(),
        input_type="IDP",
        integration_type="ACTIVITY_DATA",
        process_id="IDP",
        client_code="Marriott",
        property_code="XYZN",
        unified_property_id="67890"
    )

    @patch("src.service.dynamodb_service.DynamoDBService.truncate_inc_solds")
    def test_handle_valid_bde_event(self, mock_truncate_inc_solds):
        handler = ActivityCompletedHandler()
        handler.handle(self.MOCK_EVENT_BDE)

        mock_truncate_inc_solds.assert_called_once_with("Hilton", "ABILN")

    @patch("src.service.dynamodb_service.DynamoDBService.truncate_inc_solds")
    def test_handle_valid_idp_event(self, mock_truncate_inc_solds):
        handler = ActivityCompletedHandler()
        handler.handle(self.MOCK_EVENT_IDP)

        mock_truncate_inc_solds.assert_called_once_with("Marriott", "XYZN")

    @patch("src.service.dynamodb_service.DynamoDBService.truncate_inc_solds")
    def test_handle_invalid_event_type(self, mock_truncate_inc_solds):
        invalid_event = MagicMock()
        handler = ActivityCompletedHandler()

        with pytest.raises(AssertionError, match="event is not ActivityCompletedBDEEvent or ActivityCompletedIDPEvent"):
            handler.handle(invalid_event)

        mock_truncate_inc_solds.assert_not_called()

    @patch("src.service.dynamodb_service.DynamoDBService.truncate_inc_solds")
    def test_handle_truncate_inc_solds_failure(self, mock_truncate_inc_solds):
        mock_truncate_inc_solds.side_effect = Exception("DynamoDB error")
        handler = ActivityCompletedHandler()

        with pytest.raises(Exception, match="DynamoDB error"):
            handler.handle(self.MOCK_EVENT_BDE)

        mock_truncate_inc_solds.assert_called_once_with("Hilton", "ABILN")
