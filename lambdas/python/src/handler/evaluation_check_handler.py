import datetime
import logging
from datetime import timed<PERSON><PERSON>
from typing import override

import pandas as pd

from src.abc.event_handler import <PERSON>Handler
from src.dto.evaluation_dynamodb_input import EvaluationDynamodbTableInput
from src.enums.env_var import EnvironmentVariable
from src.events.trigger_evaluation_event import TriggerEvaluationEvent
from src.log_config.setup import setup_logger
from src.service.dynamodb_service import dynamodb_service
from src.service.sns_service import sns_service
from src.util.env_service import get_value


class EvaluationCheckHandler(EventHandler):
    logger = logging.getLogger(__name__)

    @override
    def handle(self, event: TriggerEvaluationEvent):
        assert isinstance(event, TriggerEvaluationEvent), "event is not TriggerEvaluationEvent"
        evaluation_input = EvaluationDynamodbTableInput(
            delta_occ_solds=get_value(EnvironmentVariable.PROPERTY_INC_SOLDS_TABLE, "do-table"),
            delta_lrv=get_value(EnvironmentVariable.DELTA_LRV_LATEST_TABLE, "do-table"),
            min_data_requirement=1,
            max_data_limit=28,
            calibrated_potential=get_value(EnvironmentVariable.CALIBRATED_POTENTIAL_TABLE, "do-table"),
            reference_rate=get_value(EnvironmentVariable.REF_PRICE_LATEST_TABLE,
                                     "do-table"))
        properties: pd.DataFrame = dynamodb_service.fetch_all_last_incremental_time_properties()

        if properties.empty:
            self.logger.info("No properties found in last incremental timetable")
            return

        property_to_trigger_eval = properties.drop_duplicates()

        one_hour_ago = datetime.datetime.now() - timedelta(hours=1)

        last_optimization_times_df = dynamodb_service.fetch_calib_date_for_input_properties(property_to_trigger_eval, dynamodb_service.LAST_OPTIMIZATION_TIME)
        if not last_optimization_times_df.empty:
            last_optimization_times_df['calibDate'] = pd.to_datetime(last_optimization_times_df['calibDate'], format='ISO8601')
            filtered_df = last_optimization_times_df[last_optimization_times_df['calibDate'] < one_hour_ago]
            property_to_trigger_eval = property_to_trigger_eval.merge(
                filtered_df, on=['clientCode', 'propertyCode'], how='inner'
            )

        last_dop_eval_time_df = dynamodb_service.fetch_calib_date_for_input_properties(property_to_trigger_eval, dynamodb_service.LAST_DOP_EVAL_TIME)
        if not last_dop_eval_time_df.empty:
            last_dop_eval_time_df['calibDate'] = pd.to_datetime(last_dop_eval_time_df['calibDate'], format='ISO8601')
            idp_window_filtered_df = last_dop_eval_time_df[last_dop_eval_time_df['calibDate'] < one_hour_ago]
            property_to_trigger_eval = property_to_trigger_eval.merge(
                idp_window_filtered_df, on=['clientCode', 'propertyCode'], how='inner'
            )

        (property_to_trigger_eval
             .apply(lambda row: self.send_evaluation_request(row['clientCode'], row['propertyCode'], evaluation_input),
                    axis=1))

    def send_evaluation_request(self, client_code, property_code, evaluation_input):
        sns_service.send_evaluate_idp_request(client_code, property_code, evaluation_input)
        dynamodb_service.truncate_property_last_incremental_time(client_code, property_code)


if __name__ == '__main__':
    setup_logger()
    EvaluationCheckHandler().handle(TriggerEvaluationEvent(scheduler_arn='', invocation_id='1', attempt_number=1))
