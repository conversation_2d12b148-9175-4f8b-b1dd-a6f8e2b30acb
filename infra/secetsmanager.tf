resource "aws_secretsmanager_secret" "rdl_analytic_secret" {
  name = "dyn-opt-secret"
  tags = merge(
    { "name" : "dyn-opt-secret" }
  )
}

resource "aws_secretsmanager_secret_version" "secret_version" {
  secret_id = aws_secretsmanager_secret.rdl_analytic_secret.id
  secret_string = jsonencode({
    (var.fds_auth_token) = "change_me",
    (var.g3_auth_token)  = "change_me"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}
