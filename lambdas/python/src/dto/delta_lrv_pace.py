from datetime import date, timedelta

from pydantic import BaseModel, ConfigDict, computed_field
from pydantic.alias_generators import to_camel


class DeltaLRVPace(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    decision_id: int
    accom_class_id: int
    occupancy_date: date
    lead_time: int
    delta_lrv: float
    lrv: float
    ceilingValue: int

    @computed_field
    @property
    def capture_date(self) -> date:
        return self.occupancy_date - timedelta(days=self.lead_time)
