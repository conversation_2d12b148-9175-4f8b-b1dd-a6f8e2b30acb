from typing import override

from src.abc.event_handler import <PERSON><PERSON><PERSON><PERSON>
from src.dto.calibration_s3_file_input import CalibrationS3FileInput
from src.events.ft_ac_report_ready_event import FTACReportReadyEvent
from src.service.http_service import HttpService
from src.service.s3_service import S3Service
from src.service.sns_service import SNSService
from src.util.s3_path_holder import get_s3_path_calibration, DELTA_SOLDS_FILE_NAME, DELTA_LRV_PACE_FILE_NAME, \
    REFERENCE_PRICE_PACE_FILE_NAME


class FTACReportReadyHandler(EventHandler):

    @override
    def handle(self, event: FTACReportReadyEvent):
        assert isinstance(event, FTACReportReadyEvent), "event is not FTACReportReadyEvent"

        client_code = event.client_code
        property_code = event.property_code
        base_s3_folder = get_s3_path_calibration(client_code, property_code, event.caught_up_date)
        dest_path = f'{base_s3_folder}/{DELTA_SOLDS_FILE_NAME}'
        s3_service = S3Service()
        if event.down_load_link != "no url":
            response = HttpService.download_file(event.down_load_link)
            s3_service.upload(response.content, dest_path)

        request_context = CalibrationS3FileInput(delta_occ_solds=f'{s3_service.get_base_path()}/{base_s3_folder}/{DELTA_SOLDS_FILE_NAME}',
                                                 delta_lrv=f'{s3_service.get_base_path()}/{base_s3_folder}/{DELTA_LRV_PACE_FILE_NAME}',
                                                 reference_rate=f'{s3_service.get_base_path()}/{base_s3_folder}/{REFERENCE_PRICE_PACE_FILE_NAME}',
                                                 max_data_limit=28,
                                                 min_data_requirement=1)

        SNSService().send_dyn_opt_potential_calibration_request(client_code=client_code, property_code=property_code,
                                                                calibration_s3_file_input=request_context)
