import logging
from typing import override

import pandas as pd

from src.abc.event_handler import <PERSON><PERSON>andler
from src.enums.env_var import EnvironmentVariable
from src.events.significant_solds_drift_detected_event import SignificantSoldsDriftDetectedEvent
from src.service.dynamodb_service import DynamoDBService
from src.service.g3_data_pull_service import G3DataPullServiceFactory
from src.service.s3_service import S3Service
from src.util.env_service import get_bool_value, get_value


class SignificantSoldsDriftHandler(EventHandler):

    logger = logging.getLogger(__name__)

    @override
    def handle(self, event: SignificantSoldsDriftDetectedEvent) -> None:
        assert isinstance(event, SignificantSoldsDriftDetectedEvent), "event is not SignificantSoldsDriftDetected"
        dynamodb_service = DynamoDBService()
        items_deleted = dynamodb_service.truncate_inc_solds(event.client_code, event.property_code)
        if get_bool_value(EnvironmentVariable.SAVE_INC_SOLDS, True):
            path = f'{event.client_code}/{event.property_code}/{event.caught_up_date.strftime('%Y_%m_%d')}/{event.evaluation_time.strftime('%Y_%m_%dT%H_%M_%S_%f')}_inc_solds.csv'
            S3Service().upload_df(path, pd.DataFrame.from_dict(items_deleted))
        self.logger.info(f"Truncated inc solds for client code {event.client_code} and property code {event.property_code}")

        dynamodb_service.increment_idp_count(event.client_code, event.property_code, event.caught_up_date)
        dynamodb_service.save_idp_window(event.client_code, event.property_code, event.evaluation_time,
                                         event.caught_up_date, event.max_occupancy_date)
        dynamodb_service.save_idp_window_by_roomclass(event.client_code, event.property_code, event.evaluation_time,
                                                      event.caught_up_date, event.rc_to_occupancy_date_mapping)

        dynamodb_service.update_last_dop_evaluation_time(event.client_code, event.property_code, event.evaluation_time)

# if __name__ == '__main__':
#     from datetime import date, datetime
#     event = SignificantSoldsDriftDetectedEvent(
#         client_code="Hilton",
#         property_code="AKLKA",
#         max_occupancy_date=date(2025, 12, 10),
#         caught_up_date=date(2025, 5, 28),
#         evaluation_time=datetime.now(),
#         rc_to_occupancy_date_mapping={}
#     )
#     handler = SignificantSoldsDriftHandler()
#     handler.handle(event)
