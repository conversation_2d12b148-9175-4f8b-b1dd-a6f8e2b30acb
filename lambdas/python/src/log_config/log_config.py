from src.enums.env_var import EnvironmentVariable
from src.util.env_service import get_value


class LogConfig:
    config = {
        "version": 1,
        "formatters": {
            "json_format": {
                "class": "src.log_config.json_formatter.JsonFormatter"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "json_format"
            }
        },
        "loggers": {
            "root": {
                "level": "WARN",
                "handlers": ["console"]
            },
            "src": {
                "level": "INFO"
            }
        }
    }

    def get_config(self):
        (self.with_root_log_level_set_to("WARN")
         .with_src_log_level_set_to(get_value(EnvironmentVariable.LOG_LEVEL, "INFO"))
         )
        return self.config

    def with_root_log_level_set_to(self, level):
        self.config["loggers"]["root"]["level"] = level
        return self

    def with_src_log_level_set_to(self, level):
        self.config["loggers"]["src"]["level"] = level
        return self
