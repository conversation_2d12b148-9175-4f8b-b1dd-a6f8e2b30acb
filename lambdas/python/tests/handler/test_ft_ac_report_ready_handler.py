from datetime import date
from unittest.mock import patch, MagicMock, call

from src.dto.calibration_s3_file_input import CalibrationS3FileInput
from src.events.ft_ac_report_ready_event import FTACReportReadyEvent
from src.handler.ft_ac_report_ready_handler import FTACReportReadyHandler
from src.util.s3_path_holder import DELTA_SOLDS_FILE_NAME, DELTA_LRV_PACE_FILE_NAME, REFERENCE_PRICE_PACE_FILE_NAME


class TestFtAcReportReadyHandler:

    @patch('src.handler.ft_ac_report_ready_handler.HttpService.download_file')
    @patch('src.handler.ft_ac_report_ready_handler.S3Service.upload')
    @patch('src.handler.ft_ac_report_ready_handler.SNSService.send_dyn_opt_potential_calibration_request')
    def test_handle(self, sns_mock, s3_mock, download_mock):
        file_content = b'file'
        download_mock.return_value = MagicMock(content=file_content)
        s3_mock.return_value = None
        sns_mock.return_value = None

        event = FTACReportReadyEvent(was_successful=True, response_files='file', property_code='p', property_id=12,
                             client_code='c', down_load_link='link', caught_up_date=date.today())

        FTACReportReadyHandler().handle(event)
        folder = f'c/p/calibration/{date.today()}'
        c= CalibrationS3FileInput(delta_occ_solds=f's3://S3_BUCKET_NAME/{folder}/{DELTA_SOLDS_FILE_NAME}',
                               delta_lrv=f's3://S3_BUCKET_NAME/{folder}/{DELTA_LRV_PACE_FILE_NAME}',
                               reference_rate=f's3://S3_BUCKET_NAME/{folder}/{REFERENCE_PRICE_PACE_FILE_NAME}',
                               max_data_limit=28,
                               min_data_requirement=1)
        assert download_mock.call_args_list == [call('link')]
        assert s3_mock.call_args_list == [call(file_content, f'{folder}/deltaOcc.csv')]
        assert sns_mock.call_args_list == [call(client_code='c', property_code='p',
                                                calibration_s3_file_input=c)]
