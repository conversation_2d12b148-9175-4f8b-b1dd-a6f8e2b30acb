from datetime import datetime
from typing import Callable, Self, Any

from pydantic import BaseModel, ConfigDict, field_validator, model_validator
from pydantic.alias_generators import to_camel

from src.enums.event_types import EventType
from src.enums.event_types import SupportedEvents
from src.util.pydantic_util import from_dict


class SourceSNSEvent(BaseModel):
    model_config = ConfigDict(extra='allow', alias_generator=to_camel)
    source_system: str
    event_type: EventType | str
    version: int
    event_date_time: datetime
    event_source: dict[str, Any]

    @field_validator('event_type', mode='before')
    @classmethod
    def event_type_validator(cls, v):
        try:
            status = EventType[v]
        except Exception:
            raise ValueError(f"{v} must of type EventType")
        return status

    def get_handler(self) -> Callable[[SupportedEvents], None]:
        return self.event_type.value.handler

    @model_validator(mode='after')
    @classmethod
    def event_source_validator(cls, v: Self):
        deserialized = from_dict(v.event_type.value.event_type, v.event_source)
        v.event_source = deserialized
        return v
