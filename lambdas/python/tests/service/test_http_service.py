from types import SimpleNamespace
from unittest import mock
from unittest.mock import <PERSON>Mock

from pydantic import BaseModel

import tests.util.test_pydantic as p
from src.service.http_service import HttpService


class Rocket(BaseModel):
    number: int


class TestHttpService:
    service = HttpService("base")

    @mock.patch("requests.get")
    def test_get_when_response_ok(self, requests_get: MagicMock):
        requests_get.return_value = SimpleNamespace(**{"ok": True, "json": lambda: dict(p.a)})
        a: p.A = self.service.get("path", p.A)

        assert a == p.a

    @mock.patch("requests.get")
    def test_get_paged_when_multiple_page(self, requests_get: MagicMock):
        def side_effect(*args, **kwargs):
            page_number = kwargs["params"]["page"]
            if page_number == 5:
                return SimpleNamespace(**{"ok": True, "json": lambda: []})
            return SimpleNamespace(**{"ok": True, "json": lambda: [dict(Rocket(number=page_number))]})
        requests_get.side_effect = side_effect
        result = self.service.get_paged_api("path", list[Rocket], 1)
        assert [Rocket(number=i) for i in range(0, 5)] == result
