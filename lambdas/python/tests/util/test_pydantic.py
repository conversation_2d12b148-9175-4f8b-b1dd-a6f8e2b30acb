from pydantic import BaseModel

from src.util.pydantic_util import from_dict_array, from_json, to_json, to_json_array


class B(BaseModel):
    b1: int
    b2: int


class A(BaseModel):
    a1: int
    b: B


b = B(b1=100, b2=120)
a = A(a1=20, b=b)
json_a = r'{"a1":20,"b":{"b1":100,"b2":120}}'
json_array_a = rf'[{json_a}]'


class TestPydanticUtil:

    def test_from_json_array(self):
        a_list = from_dict_array(list[A], [dict(a)])
        assert a_list == [a]

    def test_from_json(self):
        a_obj = from_json(A, json_a)
        assert a_obj == a

    def test_to_json(self):
        j = to_json(A, a)
        assert json_a.encode('utf-8') == j

    def test_to_json_array(self):
        j = to_json_array(list[A], [a])
        assert json_array_a.encode('utf-8') == j
