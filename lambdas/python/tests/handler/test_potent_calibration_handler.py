from datetime import date
from unittest.mock import patch, MagicMock

from src.events.potential_calibration_event import PotentialCalibrationEvent
from src.handler.potential_calibration_handler import PotentialCalibrationHandler
from src.service.data_refresh_service import DataRefreshServiceFactory
from src.service.dynamodb_service import DynamoDBService
from src.service.g3_data_pull_service import G3DataPullServiceFactory


class TestPotentialCalibrationHandler:

    @patch.object(DynamoDBService, 'fetch_accom_type_mapping', return_value={})
    @patch.object(DynamoDBService, 'update_accom_class_mapping')
    @patch.object(G3DataPullServiceFactory, 'init')
    @patch.object(DataRefreshServiceFactory, 'from_client_property_codes_base_url')
    def test_handle(self, mock_data_refresh_service_factory, mock_g3_service_init,
                    mock_update_accom_class_mapping, mock_fetch_accom_type_mapping):

        g3_data_pull_service_mock = MagicMock()
        mock_g3_service_init.return_value = g3_data_pull_service_mock
        g3_data_pull_service_mock.fetch_accom_types.return_value = []

        data_refresh_service_mock = MagicMock()
        mock_data_refresh_service_factory.return_value = data_refresh_service_mock

        event = PotentialCalibrationEvent(
            base_callback_u_r_l='link', property_id=12, client_code='c',
            property_code='p', caught_up_date=date.today()
        )

        PotentialCalibrationHandler().handle(event)

        mock_fetch_accom_type_mapping.assert_called_once_with('c', 'p')
        g3_data_pull_service_mock.fetch_accom_types.assert_called_once()
        mock_update_accom_class_mapping.assert_called_once_with(accom_class_mapping=[],client_code='c',property_code='p')
        data_refresh_service_mock.refresh_calibration_data.assert_called_once_with('c', 'p', date.today(), 12, 'link')
