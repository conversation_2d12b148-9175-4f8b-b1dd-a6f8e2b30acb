from typing import override, Union

from src.abc.event_handler import EventHandler
from src.events.activity_completed_bde_event import ActivityCompletedBDEEvent
from src.events.activity_completed_idp_event import ActivityCompletedIDPEvent
from src.service.dynamodb_service import DynamoDBService


class ActivityCompletedHandler(EventHandler):

    @override
    def handle(self, event:  Union[ActivityCompletedBDEEvent, ActivityCompletedIDPEvent]) -> None:
        assert isinstance(event, (ActivityCompletedBDEEvent, ActivityCompletedIDPEvent)), "event is not ActivityCompletedBDEEvent or ActivityCompletedIDPEvent"
        dynamodb_service = DynamoDBService()
        dynamodb_service.truncate_inc_solds(event.client_code, event.property_code)
