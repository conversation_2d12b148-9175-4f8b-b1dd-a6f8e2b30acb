module "dyn_opt_data_s3" {
  source                   = "terraform-aws-modules/s3-bucket/aws"
  version                  = "4.1.1"
  bucket                   = var.environment == "prod" ? "dyn-opt-data" : "${var.environment}-dyn-opt-data"
  acl                      = "private"
  control_object_ownership = true
  object_ownership         = "ObjectWriter"
  versioning = {
    enabled = false
  }
  lifecycle_rule = [
    {
      id      = "data_expiration_rule"
      enabled = true
      expiration = {
        days = 60
      }
      filter = {
        prefix = ""
      }
    }
  ]
}
