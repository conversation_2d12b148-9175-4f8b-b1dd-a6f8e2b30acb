from datetime import date

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel


class ReportContext(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    start_date: date
    end_date: date


class GenerateFTACReport(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    base_callback_U_R_L: str
    property_id: int
    client_code: str
    property_code: str
    caught_up_date: date
    report_context: ReportContext
    destination_s3_path: str
