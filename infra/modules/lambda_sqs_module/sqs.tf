locals {
  sqs_visibility_timeout_seconds = (
    var.sqs.visibility_timeout_seconds != null ?
    var.sqs.visibility_timeout_seconds :                        # use the provided value
    var.lambda.timeout + var.sqs.visibility_timeout_add_seconds # or add N seconds to the lambda timeout
  )
}

module "sqs_queue_for_lambda" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 4.1"

  name                        = var.lambda.name
  visibility_timeout_seconds  = local.sqs_visibility_timeout_seconds
  fifo_queue                  = var.sqs.fifo_queue
  content_based_deduplication = var.sqs.content_based_deduplication
  create_dlq                  = var.sqs.create_dlq
  redrive_policy = {
    maxReceiveCount = var.sqs.redrive_policy.maxReceiveCount
  }
  sqs_managed_sse_enabled = var.sqs.sqs_managed_sse_enabled

  queue_policy_statements = var.sqs.queue_policy_statements

  tags = var.tags
}
