import json
import logging
from datetime import datetime
from typing import override

from src.context_vars.property_context import property_context_var
from src.dto.property_context_info import PropertyContextInfo


class JsonFormatter(logging.Formatter):

    @override
    def format(self, record):
        property_context_dto: PropertyContextInfo = property_context_var.get()
        log_data = {
            'timestamp': self.formatTime(record),
            'level': record.levelname,
            'message': record.getMessage(),
            'logger_name': record.name,
            'team': 'ans',
            'service': 'dyn-opt',
            'property_code': property_context_dto.property_code,
            'client_code': property_context_dto.client_code
        }
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        return json.dumps(log_data)

    @override
    def formatException(self, exc_info):
        return repr(super().formatException(exc_info))

    @override
    def formatTime(self, record, datefmt = None):
        return datetime.fromtimestamp(record.created).astimezone().isoformat(timespec='milliseconds')

