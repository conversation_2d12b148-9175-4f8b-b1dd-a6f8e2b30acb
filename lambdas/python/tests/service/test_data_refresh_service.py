from datetime import date, <PERSON><PERSON><PERSON>
from decimal import Decimal
from unittest.mock import patch, MagicMock, call

import numpy as np
import pandas as pd
import pytest

from src.dto.delta_lrv_evaluation import DeltaLRVEvaluation
from src.dto.delta_lrv_pace import DeltaLRVPace
from src.dto.reference_price_pace import ReferencePricePace
from src.service.data_refresh_service import DataRefreshService
from src.service.dynamodb_service import DynamoDBService
from src.service.g3_any_api_service import G3AnyApiService
from src.service.g3_data_pull_service import G3DataPullService
from src.service.s3_service import S3Service
from src.service.sns_service import SNSService
from src.util.s3_path_holder import DELTA_LRV_PACE_FILE_NAME, REFERENCE_PRICE_PACE_FILE_NAME


class TestDataRefreshService:
    __CLIENT_CODE = 'client_code'
    __PROPERTY_CODE = 'property_code'
    __CAUGHT_UP_DATE = date.today()
    __g3_base_url = 'url'

    @pytest.fixture
    def service(self) -> DataRefreshService:
        g3_service_mock = MagicMock(autospec=G3DataPullService.__class__)
        dynamodb_service_mock = MagicMock(autospec=DynamoDBService.__class__)
        s3_service_mock = MagicMock(autospec=S3Service.__class__)
        sns_service_mock = MagicMock(autospec=SNSService.__class__)
        g3_any_api_service = MagicMock(autospec=G3AnyApiService.__class__)
        return DataRefreshService(g3_service_mock, dynamodb_service_mock, s3_service_mock,
                                  sns_service_mock, g3_any_api_service)

    @patch('src.service.data_refresh_service.DataRefreshService.use_any_api')
    @patch('src.service.data_refresh_service.to_df')
    def test_refresh_calibration_data(self, to_df_mock, use_any_api: MagicMock,service) -> None:
        delta_lrv_pace = DeltaLRVPace(decision_id=1, accom_class_id=1, occupancy_date=self.__CAUGHT_UP_DATE,
                                      lead_time=1, delta_lrv=1.0, lrv=1.0, ceilingValue=1)
        use_any_api.return_value = False
        service.g3_data_pull_service.fetch_delta_lrv_pace_for_calibration.return_value = [delta_lrv_pace]
        service.s3_service.upload_df.return_value = None
        service.s3_service.get_base_path.return_value = 'base'
        ref_pace = ReferencePricePace(accom_class_id=1, occupancy_date=self.__CAUGHT_UP_DATE, lead_time=1, price=1.0)
        service.g3_data_pull_service.fetch_reference_rate_pace_for_calibration.return_value = [ref_pace]
        service.sns_service.send_trigger_ac_sold_pace_event.return_value = None
        to_df_mock.return_value = 'some df'

        service.refresh_calibration_data(self.__CLIENT_CODE, self.__PROPERTY_CODE, self.__CAUGHT_UP_DATE,
                                         120, self.__g3_base_url)

        s3_call_args_list = [call(
            dest_path=f'{self.__CLIENT_CODE}/{self.__PROPERTY_CODE}/calibration/{self.__CAUGHT_UP_DATE}/{DELTA_LRV_PACE_FILE_NAME}',
            df='some df'),
            call(dest_path=f'{self.__CLIENT_CODE}/{self.__PROPERTY_CODE}/calibration/{self.__CAUGHT_UP_DATE}/{REFERENCE_PRICE_PACE_FILE_NAME}',
                df='some df')]
        delta_s3 = f'base/{self.__CLIENT_CODE}/{self.__PROPERTY_CODE}/calibration/{self.__CAUGHT_UP_DATE}/deltaOcc.csv'
        start_date = self.__CAUGHT_UP_DATE - timedelta(days=180)

        assert to_df_mock.call_args_list == [call(list[DeltaLRVPace], [delta_lrv_pace]), call(list[ReferencePricePace], [ref_pace])]
        assert service.s3_service.upload_df.call_args_list == s3_call_args_list
        assert (service.sns_service.send_trigger_ac_sold_pace_event.call_args_list ==
                [call(self.__CLIENT_CODE, self.__PROPERTY_CODE,120, self.__g3_base_url, start_date, start_date + timedelta(days=900), self.__CAUGHT_UP_DATE, delta_s3)])

        assert service.g3_data_pull_service.fetch_reference_rate_pace_for_calibration.call_args_list == [call(start_date, self.__CAUGHT_UP_DATE)]
        assert service.g3_data_pull_service.fetch_delta_lrv_pace_for_calibration.call_args_list == [call(start_date, self.__CAUGHT_UP_DATE)]

    def test_refresh_evaluation_data_store_data(self, service):
        list_of_accom = [5, 3, 8]
        new_lrv = [DeltaLRVEvaluation(accom_class_id=i, occupancy_date=self.__CAUGHT_UP_DATE,lead_time=0,
                           delta_lrv=Decimal(1), capture_date=self.__CAUGHT_UP_DATE, ceiling_value=1, lrv=Decimal(i)) for i in
                   list_of_accom]
        recent_prices = 'prices'
        service.g3_any_api_service.fetch.return_value = pd.DataFrame({'maxOccDate': [self.__CAUGHT_UP_DATE + timedelta(days=300)]})
        service.g3_data_pull_service.fetch_recent_lrv.return_value = new_lrv
        service.dynamo_service.get_current_lrv.return_value = pd.DataFrame({
            'accomClassId': list_of_accom,
            'occupancyDate': [self.__CAUGHT_UP_DATE for _ in list_of_accom],
            'leadTime': [0 for _ in list_of_accom],
            'lrv': [10, 20, 5]
        })
        service.g3_data_pull_service.fetch_recent_reference_rate.return_value = recent_prices

        service.refresh_evaluation_data_store_lrv_change(self.__CAUGHT_UP_DATE, self.__CLIENT_CODE,
                                                         self.__PROPERTY_CODE)

        assert service.dynamo_service.update_delta_lrv_latest.call_args_list == [call(new_lrv, self.__CLIENT_CODE, self.__PROPERTY_CODE)]
        assert service.dynamo_service.update_ref_price_latest.call_args_list == [call(recent_prices, self.__CLIENT_CODE, self.__PROPERTY_CODE)]
        assert service.dynamo_service.update_decision_change.call_args_list == [call(np.median([5, 17, -3]), self.__CLIENT_CODE, self.__PROPERTY_CODE, self.__CAUGHT_UP_DATE)]
