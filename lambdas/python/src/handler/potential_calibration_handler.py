from typing import override

from src.abc.event_handler import <PERSON><PERSON><PERSON><PERSON>
from src.events.bde_completed_event import B<PERSON><PERSON>ompletedEvent
from src.events.potential_calibration_event import PotentialCalibrationEvent
from src.service.data_refresh_service import DataRefreshServiceFactory
from src.service.dynamodb_service import DynamoDBService
from src.service.g3_data_pull_service import G3DataPullServiceFactory


class PotentialCalibrationHandler(EventHandler):

    @override
    def handle(self, event: PotentialCalibrationEvent | BDECompletedEvent):
        dynamodb_service = DynamoDBService()
        client_code = event.client_code
        code = event.property_code
        base_callback_u_r_l = event.base_callback_u_r_l
        g3_data_pull_service = G3DataPullServiceFactory.init(event.base_callback_u_r_l,
                                                             client_code, code)

        accom_class_id = dynamodb_service.fetch_accom_type_mapping(client_code, code)
        if len(accom_class_id) == 0:
            accom_types = g3_data_pull_service.fetch_accom_types()
            dynamodb_service.update_accom_class_mapping(accom_class_mapping=accom_types, client_code=client_code, property_code=code)

        data_refresh_service = DataRefreshServiceFactory.from_client_property_codes_base_url(client_code, code,
                                                                                             base_callback_u_r_l, event.property_id)
        data_refresh_service.refresh_calibration_data(client_code, code, event.caught_up_date,
                                                      event.property_id, base_callback_u_r_l)
