output "sqs_arn" {
  value       = module.sqs_queue_for_lambda.queue_arn
  description = "SQS ARN"
}

output "sqs_url" {
  value       = module.sqs_queue_for_lambda.queue_id
  description = "SQS URL"
}

output "lambda_alias_arn" {
  value       = module.lambda_alias.lambda_alias_arn
  description = "SQS URL"
}

output "codedeploy_iam_role_name" {
  value       = module.deploy.codedeploy_iam_role_name
  description = "codedeploy IAM role name"
}