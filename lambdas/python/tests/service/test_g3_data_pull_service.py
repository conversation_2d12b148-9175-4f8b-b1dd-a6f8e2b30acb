from datetime import date
from unittest.mock import MagicMock, call

import pytest

from src.dto.accom_class import AccomClass
from src.dto.accom_type import AccomType
from src.dto.delta_lrv_pace import DeltaLRVPace
from src.dto.reference_price_pace import ReferencePricePace
from src.service.g3_data_pull_service import G3DataPullService
from src.service.http_service import HttpService


class TestG3DataPullService:
    __params: dict = {'clientCode': 'c', 'propertyCode': 'p'}

    @pytest.fixture
    def service(self) -> G3DataPullService:
        http_service_mock = MagicMock(autospec=HttpService("https://g3.com"))
        return G3DataPullService(http_service_mock, client_code='c', property_code='p')

    def test_fetch_accom_types(self, service: G3DataPullService) -> None:
        expected_accom_types = [
            AccomType(id=i, name=str(i), accom_type_code=str(i),
                      accom_class=AccomClass(id=i, code=str(i), name=str(i)), status_id=1, system_default=0)
            for i in range(0, 2)]
        service.g3.get.return_value = expected_accom_types
        accom_type: list[AccomType] = service.fetch_accom_types()

        assert accom_type == expected_accom_types
        assert service.g3.get.call_args == call(service.ACCOM_TYPE_MAPPING_PATH, return_type=list[AccomType],
                                                params=self.__params)

    def test_fetch_lrv_calibration_data(self, service: G3DataPullService):
        expected_lrv_calibration_data = [
            DeltaLRVPace(decision_id=i, accom_class_id=i, occupancy_date=date.today(), lead_time=i, delta_lrv=i * 0.1,
                         lrv=i, ceilingValue=i)
            for i in range(0, 2)
        ]
        service.g3.get_paged_api.return_value = expected_lrv_calibration_data
        lrv_pace = service.fetch_delta_lrv_pace_for_calibration(date.today(), date.today())

        assert lrv_pace == expected_lrv_calibration_data
        params = {'startBDEDate': date.today(), 'endBDEDate': date.today()}
        params.update(self.__params)
        assert service.g3.get_paged_api.call_args == call(service.CALIBRATION_DELTA_LRV_PACE_DATA,
                                                          return_type=list[DeltaLRVPace],
                                                          page_size=28000, params=params)

    def test_fetch_reference_price_calibration_data(self, service: G3DataPullService):
        expected_reference_price_calibration_data = [
            ReferencePricePace(accom_class_id=i, occupancy_date=date.today(), lead_time=i, price=i * 0.1,
                               capture_date=date.today())
            for i in range(0, 2)
        ]
        service.g3.get_paged_api.return_value = expected_reference_price_calibration_data
        prices = service.fetch_reference_rate_pace_for_calibration(date.today(), date.today())

        assert prices == expected_reference_price_calibration_data
        params = {'startBDEDate': date.today(), 'endBDEDate': date.today()}
        params.update(self.__params)
        assert service.g3.get_paged_api.call_args == call(service.CALIBRATION_REF_PRICE_PACE_DATA,
                                                          return_type=list[ReferencePricePace],
                                                          page_size=28000, params=params)
