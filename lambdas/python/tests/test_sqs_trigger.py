import json
from unittest.mock import patch, MagicMock, call

from src.events.accom_class_mapping_changed_event import AccomClassMappingChangedEvent
from src.sqs_trigger import lambda_handler
from src.util.pydantic_util import from_json


@patch('src.dto.sqs_event.SourceSNSEvent.get_handler')
def test_accom_config_change_handler(handle_mock: MagicMock) -> None:
    event_source = json.dumps(
        {'caughtUpDate': '2024-01-01', 'baseCallbackURL': 'http://localhost', 'propertyId': 1, 'clientCode': 'c',
         'propertyCode': 'p'})
    event = {
        'Records': [
            {
                'body': json.dumps({'sourceSystem': 'local',
                                    'eventType': 'ACCOMODATION_CONFIG_CHANGE',
                                    'version': 1,
                                    'eventDateTime': '2023-02-28T00:00:00Z',
                                    'eventSource': event_source
                                    })
            }
        ]
    }
    mock = MagicMock()
    handle_mock.return_value = mock
    lambda_handler(event, None)

    assert mock.call_args == call(from_json(AccomClassMappingChangedEvent, event_source))


@patch('src.dto.sqs_event.SourceSNSEvent.get_handler')
def test_forecast_config_change_handler(handle_mock: MagicMock) -> None:
    event_source = json.dumps(
        {'caughtUpDate': '2024-01-01', 'baseCallbackURL': 'http://localhost', 'propertyId': 1, 'clientCode': 'c',
         'propertyCode': 'p'})
    event = {
        'Records': [
            {
                'body': json.dumps({'sourceSystem': 'local',
                                    'eventType': 'FORECAST_GROUP_CONFIG_CHANGED',
                                    'version': 1,
                                    'eventDateTime': '2023-02-28T00:00:00Z',
                                    'eventSource': event_source
                                    })
            }
        ]
    }
    mock = MagicMock()
    handle_mock.return_value = mock
    lambda_handler(event, None)

    assert mock.call_args == call(from_json(AccomClassMappingChangedEvent, event_source))
