# IAM Role for EventBridge Scheduler to invoke the Lambda
resource "aws_iam_role" "scheduler_invoke_role" {
  name = "eventbridge_scheduler_invoke_lambda"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Principal = {
          Service = "scheduler.amazonaws.com"
        },
        Effect = "Allow",
        Sid    = ""
      }
    ]
  })
}

# IAM Policy to allow invoking the Lambda function
resource "aws_iam_role_policy" "lambda_invoke_policy" {
  name = "invoke_lambda_policy"
  role = aws_iam_role.scheduler_invoke_role.id
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action   = "lambda:InvokeFunction",
        Effect   = "Allow",
        Resource = module.evaluation_check.lambda_alias_arn
      }
    ]
  })
}

# AWS EventBridge Scheduler to invoke the Lambda
resource "aws_scheduler_schedule" "lambda_scheduler" {
  name = "dyn-opt-evaluation-scheduler"

  flexible_time_window {
    mode = "OFF"
  }

  schedule_expression = "rate(${var.eval_time_interval} minutes)" # Adjust your interval here, e.g., cron or rate expression

  target {
    arn      = module.evaluation_check.lambda_alias_arn
    role_arn = aws_iam_role.scheduler_invoke_role.arn

    input = <<EOT
{
    "eventDateTime": "<aws.scheduler.scheduled-time>",
    "eventSource": "{\"attemptNumber\":\"<aws.scheduler.attempt-number>\",\"invocationId\":\"<aws.scheduler.execution-id>\",\"schedulerArn\":\"<aws.scheduler.schedule-arn>\"}",
    "eventType": "TRIGGER_EVALUATION",
    "sourceSystem": "awsEventScheduler",
    "version": 1
}
EOT
  }
}
