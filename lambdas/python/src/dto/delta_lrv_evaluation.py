from datetime import date
from decimal import Decimal

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

from src.dto.delta_lrv import DeltaLRV


class DeltaLRVEvaluation(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    accom_class_id: int
    occupancy_date: date
    lead_time: int
    delta_lrv: Decimal
    capture_date: date
    ceiling_value: int
    lrv: Decimal


    @staticmethod
    def from_delta_lrv(d: DeltaLRV, caught_up_date: date):
        return DeltaLRVEvaluation(accom_class_id=d.accom_class_id, occupancy_date=d.occupancy_date,
                                  lead_time=(d.occupancy_date - caught_up_date).days,
                                  delta_lrv=Decimal(str(d.delta_lrv)), capture_date=caught_up_date,
                                  lrv=Decimal(str(d.lrv)), ceiling_value=d.ceiling_value)

    def dynamodb_record(self, client_code, property_code):
        return {
            'clientCode_propertyCode': self.get_partition_key(client_code, property_code),
            'clientCode': client_code,
            'propertyCode': property_code,
            'accomClassId': self.accom_class_id,
            'occupancyDate': self.occupancy_date.isoformat(),
            'leadTime': self.lead_time,
            'captureDate': self.capture_date.isoformat(),
            'deltaLRV': self.delta_lrv,
            'accomClassId_occupancyDate': f'{self.accom_class_id}_{self.occupancy_date.isoformat()}',
            'lrv': self.lrv,
            'ceilingValue': self.ceiling_value
        }

    @staticmethod
    def get_partition_key(client_code, property_code):
        return f'{client_code}_{property_code}'
