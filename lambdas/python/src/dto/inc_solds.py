from typing import Self

from pydantic import <PERSON><PERSON>odel, ConfigDict, computed_field
from pydantic.alias_generators import to_camel


class IncrementalSolds(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    property_code: str
    client_code: str
    accom_class_id: int
    occupancy_date: str
    inc_solds: int
    last_updated_time: str


    @computed_field(alias='clientCode_propertyCode', alias_priority=3)
    @property
    def clientCode_propertyCode(self) -> str:
        return f'{self.client_code}_{self.property_code}'

    @computed_field(alias='accomClassId_occupancyDate', alias_priority=3)
    @property
    def accomClassId_occupancyDate(self) -> str:
        return f'{self.accom_class_id}_{self.occupancy_date}'


    @staticmethod
    def get_partition_key(client_code: str, property_code: str):
        return f'{client_code}_{property_code}'


    def __hash__(self):
        return hash((self.property_code, self.client_code, self.accom_class_id, self.occupancy_date))

    def __eq__(self, other: Self):
        return (other.client_code == self.client_code and
                other.property_code == self.property_code and
                other.accom_class_id == self.accom_class_id and
                other.occupancy_date == self.occupancy_date)
