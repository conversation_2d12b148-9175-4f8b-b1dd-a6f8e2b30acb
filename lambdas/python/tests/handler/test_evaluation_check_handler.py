from unittest.mock import patch, MagicMock, call

import pandas as pd

from src.dto.evaluation_dynamodb_input import EvaluationDynamodbTableInput
from src.events.trigger_evaluation_event import TriggerEvaluationEvent
from src.handler.evaluation_check_handler import EvaluationCheckHandler


class TestEvaluationCheckHandler:

    @patch('src.handler.evaluation_check_handler.dynamodb_service.truncate_property_last_incremental_time')
    @patch('src.handler.evaluation_check_handler.sns_service.send_evaluate_idp_request')
    @patch('src.handler.evaluation_check_handler.dynamodb_service.fetch_all_last_incremental_time_properties')
    def test_evaluation_check_handler(self, fetch_last_inc_mock: MagicMock, sns_idp: MagicMock, truncate_last_inc_time: MagicMock) -> None:
        sns_idp.return_value = None
        truncate_last_inc_time.return_value = None
        fetch_last_inc_mock.return_value = pd.DataFrame({
            'propertyCode': ['A', 'B', 'B', 'B'],
            'clientCode': ['X', 'X', 'Y', 'Y']
        })
        e = TriggerEvaluationEvent(scheduler_arn='a', invocation_id='a', attempt_number=1)
        EvaluationCheckHandler().handle(e)
        evaluation_input = EvaluationDynamodbTableInput(
            delta_occ_solds="do-table",
            delta_lrv="do-table",
            min_data_requirement=1,
            max_data_limit=28,
            calibrated_potential="do-table",
            reference_rate="do-table")

        assert sns_idp.call_args_list == [
            call('X', 'A', evaluation_input),
            call('X', 'B', evaluation_input),
            call('Y', 'B', evaluation_input)
        ]
        assert truncate_last_inc_time.call_args_list == [
            call('X', 'A'),
            call('X', 'B'),
            call('Y', 'B')
        ]
        fetch_last_inc_mock.assert_called_once()


    @patch('src.handler.evaluation_check_handler.sns_service.send_evaluate_idp_request')
    @patch('src.handler.evaluation_check_handler.dynamodb_service.fetch_all_last_incremental_time_properties')
    def test_evaluation_check_handler_no_properties(self, fetch_last_inc_mock: MagicMock, sns_idp: MagicMock) -> None:
        sns_idp.return_value = None
        fetch_last_inc_mock.return_value = pd.DataFrame()
        e = TriggerEvaluationEvent(scheduler_arn='a', invocation_id='a', attempt_number=1)
        EvaluationCheckHandler().handle(e)
        assert sns_idp.call_args_list == []
        fetch_last_inc_mock.assert_called_once()



