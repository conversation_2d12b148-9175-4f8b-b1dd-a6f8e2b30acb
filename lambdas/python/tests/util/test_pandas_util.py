from datetime import date

import pandas as pd
from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

from src.dto.delta_lrv_pace import DeltaLRVPace
from src.util.pandas_util import to_df


class Student(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    name: str
    roll_no: int


class TestPandasUtil:

    def test_to_df_when_no_computed_fields(self):
        actual_df = to_df(list[Student], [Student(name="A", roll_no=1)])
        pd.testing.assert_frame_equal(actual_df, pd.DataFrame({"name": "A", "rollNo": 1}, index=[0]))

    def test_to_df_when_computed_fields(self):
        pace = DeltaLRVPace(decision_id=1, accom_class_id=1, occupancy_date=date.today(), lead_time=0, delta_lrv=1.1,
                            lrv=1, ceilingValue=1)
        df = to_df(list[DeltaLRVPace], [pace])
        pd.testing.assert_frame_equal(df, pd.DataFrame(
            {"decisionId": 1, "accomClassId": 1, "occupancyDate": date.today(), "leadTime": 0, "deltaLrv": 1.1,
             "lrv": 1.0, "ceilingValue": 1, "captureDate": date.today()}, index=[0]))
