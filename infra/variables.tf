variable "environment" {
  description = "The environment name in which the ECS Service is located. (e.g. dev, stage, prod)"
  type        = string
}

variable "service_name" {
  type        = string
  description = "Service name. Will be used in resources name"
  default     = "dyn-opt"
}

variable "default_page_size_g3" {
  default     = 28000
  description = "default page size to use to fetch data from g3"
  type        = number
}

variable "repository_name" {
  description = "The name of the repository. Will be used as a tag for resources."
  type        = string
  default     = "dynamic-optimization-lambdas"
}

variable "project_short_name" {
  description = "Project Short Names to append before resources"
  type        = string
  default     = "dyn-opt"
}

variable "fds_uen_arn" {
  description = "FDS SNS Arn"
  type        = string
}

variable "secret_manager_name" {
  description = "Secret manager name"
  type        = string
  default     = "dyn-opt-secret"
}


# VPC lookups
variable "us_east_2_vpc_id" {
  description = "The VPC ID for the us-east-2 region"
  type        = string
}
variable "us_east_2a_private_subnet_id" {
  description = "The private subnet ID for the us-east-2a availability zone"
  type        = string
}
variable "us_east_2b_private_subnet_id" {
  description = "The private subnet ID for the us-east-2b availability zone"
  type        = string
}
variable "us_east_2c_private_subnet_id" {
  description = "The private subnet ID for the us-east-2c availability zone"
  type        = string
}
variable "integration_setting_url" {
  type        = string
  description = "Base url for integration setting"
}

variable "eval_time_interval" {
  type        = number
  description = "Evaluation scheduler interval in minutes"
}

variable "fds_auth_token" {
  default     = "fds_auth_token"
  description = "key"
  type        = string
}

variable "g3_auth_token" {
  default     = "g3_auth_token"
  description = "key"
  type        = string
}

variable "ucs_base_url" {
  description = "Base url for ucs service"
  type        = string
}

variable "uis_base_url" {
  description = "UIS service base url"
  type        = string
}

variable "g3_base_url" {
  description = "G3 base URL"
  type        = string
}