module "dyn-opt-accom-type-mapping" {
  source  = "terraform-aws-modules/dynamodb-table/aws"
  version = "3.3.0"

  name = "dyn-opt-accom-type-mapping"

  autoscaling_enabled = false
  billing_mode        = "PAY_PER_REQUEST"

  hash_key  = "clientCode_propertyCode" #pk
  range_key = "accomType"               #sk

  attributes = [
    {
      name = "clientCode_propertyCode"
      type = "S"
    },
    {
      name = "accomType"
      type = "S"
    }
  ]
}

module "dyn_opt_property_incremental_solds" {
  source  = "terraform-aws-modules/dynamodb-table/aws"
  version = "3.3.0"

  name = "dyn-opt-property-incremental-solds"

  autoscaling_enabled = false
  billing_mode        = "PAY_PER_REQUEST"

  hash_key  = "clientCode_propertyCode"    #pk
  range_key = "accomClassId_occupancyDate" #sk

  attributes = [
    {
      name = "clientCode_propertyCode"
      type = "S"
    },
    {
      name = "accomClassId_occupancyDate"
      type = "S"
    }
  ]
}

module "dyn_opt_delta_lrv_latest" {
  source  = "terraform-aws-modules/dynamodb-table/aws"
  version = "3.3.0"

  name = "dyn-opt-delta-lrv-latest"

  autoscaling_enabled = false
  billing_mode        = "PAY_PER_REQUEST"

  hash_key  = "clientCode_propertyCode"    #pk
  range_key = "accomClassId_occupancyDate" #sk

  attributes = [
    {
      name = "clientCode_propertyCode"
      type = "S"
    },
    {
      name = "accomClassId_occupancyDate"
      type = "S"
    }
  ]
}

module "dyn_opt_available_capacity" {
  source  = "terraform-aws-modules/dynamodb-table/aws"
  version = "3.3.0"

  name = "dyn-opt-available-capacity"

  autoscaling_enabled = false
  billing_mode        = "PAY_PER_REQUEST"

  hash_key  = "clientCode_propertyCode"    #pk
  range_key = "accomClassId_occupancyDate" #sk

  attributes = [
    {
      name = "clientCode_propertyCode"
      type = "S"
    },
    {
      name = "accomClassId_occupancyDate"
      type = "S"
    }
  ]
}

module "dyn_opt_ref_price_latest" {
  source  = "terraform-aws-modules/dynamodb-table/aws"
  version = "3.3.0"

  name = "dyn-opt-ref-price-latest"

  autoscaling_enabled = false
  billing_mode        = "PAY_PER_REQUEST"

  hash_key  = "clientCode_propertyCode"    #pk
  range_key = "accomClassId_occupancyDate" #sk

  attributes = [
    {
      name = "clientCode_propertyCode"
      type = "S"
    },
    {
      name = "accomClassId_occupancyDate"
      type = "S"
    }
  ]
}

module "dyn_opt_calibrated_potentials" {
  source  = "terraform-aws-modules/dynamodb-table/aws"
  version = "3.3.0"

  name = "dyn-opt-calibrated-potentials"

  autoscaling_enabled = false
  billing_mode        = "PAY_PER_REQUEST"

  hash_key  = "clientCode_propertyCode" #pk
  range_key = "accomClassId_metricType" #sk

  attributes = [
    {
      name = "clientCode_propertyCode"
      type = "S"
    },
    {
      name = "accomClassId_metricType"
      type = "S"
    }
  ]
}

module "dyn_opt_idp_count" {
  source  = "terraform-aws-modules/dynamodb-table/aws"
  version = "3.3.0"

  name = "dyn-opt-idp-count"

  autoscaling_enabled = false
  billing_mode        = "PAY_PER_REQUEST"

  hash_key  = "clientCode_propertyCode" #pk
  range_key = "caughtUpDate"            #sk

  attributes = [
    {
      name = "clientCode_propertyCode"
      type = "S"
    },
    {
      name = "caughtUpDate"
      type = "S"
    }
  ]

  ttl_enabled        = true
  ttl_attribute_name = "ttl"
}

module "dyn_opt_idp_window" {
  source  = "terraform-aws-modules/dynamodb-table/aws"
  version = "3.3.0"

  name = "dyn-opt-idp-window"

  autoscaling_enabled = false
  billing_mode        = "PAY_PER_REQUEST"

  hash_key  = "clientCode_propertyCode"     #pk
  range_key = "caughtUpDate_evaluationTime" #sk

  attributes = [
    {
      name = "clientCode_propertyCode"
      type = "S"
    },
    {
      name = "caughtUpDate_evaluationTime"
      type = "S"
    }
  ]

  ttl_enabled        = true
  ttl_attribute_name = "ttl"
}

module "dyn_opt_calib_date" {
  source  = "terraform-aws-modules/dynamodb-table/aws"
  version = "3.3.0"

  name = "dyn-opt-calib-date"

  autoscaling_enabled = false
  billing_mode        = "PAY_PER_REQUEST"

  hash_key  = "clientCode_propertyCode" #pk
  range_key = "calibrationItem"         #sk

  attributes = [
    {
      name = "clientCode_propertyCode"
      type = "S"
    },
    {
      name = "calibrationItem"
      type = "S"
    }
  ]
}

module "dyn_opt_property_last_incremental_time" {
  source  = "terraform-aws-modules/dynamodb-table/aws"
  version = "3.3.0"

  name = "dyn-opt-property-last-incremental-time"

  autoscaling_enabled = false
  billing_mode        = "PAY_PER_REQUEST"

  hash_key  = "clientCode"   #pk
  range_key = "propertyCode" #sk

  attributes = [
    {
      name = "clientCode"
      type = "S"
    },
    {
      name = "propertyCode"
      type = "S"
    }
  ]
}

module "dyn_opt_decision_change" {
  source  = "terraform-aws-modules/dynamodb-table/aws"
  version = "3.3.0"

  name = "dyn-opt-decision-change"

  autoscaling_enabled = false
  billing_mode        = "PAY_PER_REQUEST"

  hash_key  = "clientCode_propertyCode"       #pk
  range_key = "caughtUpDate_optimizationTime" #sk

  attributes = [
    {
      name = "clientCode_propertyCode"
      type = "S"
    },
    {
      name = "caughtUpDate_optimizationTime"
      type = "S"
    }
  ]

  ttl_enabled        = true
  ttl_attribute_name = "ttl"
}

module "dyn_opt_idp_window_by_roomclass" {
  source  = "terraform-aws-modules/dynamodb-table/aws"
  version = "3.3.0"

  name = "dyn-opt-idp-window-by-roomclass"

  autoscaling_enabled = false
  billing_mode        = "PAY_PER_REQUEST"

  hash_key  = "clientCode_propertyCode"                  #pk
  range_key = "caughtUpDate_evaluationTime_accomClassId" #sk

  attributes = [
    {
      name = "clientCode_propertyCode"
      type = "S"
    },
    {
      name = "caughtUpDate_evaluationTime_accomClassId"
      type = "S"
    }
  ]

  ttl_enabled        = true
  ttl_attribute_name = "ttl"
}
