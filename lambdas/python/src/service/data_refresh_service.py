from datetime import date, <PERSON><PERSON><PERSON>
from decimal import Decimal

import numpy as np
import pandas as pd
import logging
from src.dto.accom_available_capacity import AccomAvailableCapacity

from src.dto.delta_lrv_evaluation import DeltaLRVEvaluation
from src.dto.delta_lrv_pace import DeltaLRVPace
from src.dto.reference_price_pace import ReferencePricePace
from src.dto.ucs_param import UCSParam
from src.enums.env_var import EnvironmentVariable
from src.service.dynamodb_service import DynamoDBService
from src.service.g3_any_api_service import G3AnyApiService, DeltaLrvPaceRequest, MaxLrvDecisionOccDate, \
    AccomAvailableCapacityRequest, CdpScheduleRequest
from src.service.g3_data_pull_service import G3DataPullService, G3DataPullServiceFactory
from src.service.http_service import HttpService
from src.service.s3_service import S3Service
from src.service.sns_service import SNSService
from src.service.ucs_service import ucs_service, UCSConfigParam
from src.util.env_service import get_int_value, get_bool_value
from src.util.g3_auth import G3Auth
from src.util.pandas_util import to_df
from src.util.perf import timeit
from src.util.s3_path_holder import get_s3_path_calibration, DELTA_LRV_PACE_FILE_NAME, REFERENCE_PRICE_PACE_FILE_NAME


class DataRefreshService:
    logger = logging.getLogger(__name__)

    def __init__(self, g3_data_pull_service: G3DataPullService, dynamodb_service: DynamoDBService,
                 s3_service: S3Service,
                 sns_service: SNSService, g3_any_api_service: G3AnyApiService):
        self.g3_data_pull_service = g3_data_pull_service
        self.dynamo_service = dynamodb_service
        self.s3_service = s3_service
        self.sns_service = sns_service
        self.g3_any_api_service = g3_any_api_service

    @timeit()
    def refresh_calibration_data(self, client_code, property_code, caught_up_date: date, property_id, g3_base_call_url):
        no_of_past_bde_to_consider = get_int_value(EnvironmentVariable.NO_OF_PAST_BDE_TO_CONSIDER, 180)
        start_bde_date = caught_up_date - timedelta(days=no_of_past_bde_to_consider)
        self.refresh_calibration_delta_lrv_pace(caught_up_date, client_code, property_code, start_bde_date)
        self.refresh_calibration_reference_rate(caught_up_date, client_code, property_code, start_bde_date)
        total_scheduled_cdp = self.g3_any_api_service.fetch(CdpScheduleRequest())['cdpScheduleId'].count()
        self.logger.info(f'Total scheduled IDPs for {client_code}, {property_code} - {total_scheduled_cdp}')
        ucs_service.update_param(client_code=client_code, property_code=property_code, param_name=UCSConfigParam.TOTAL_SCHEDULED_IDP_COUNT.get_full_name(), new_value=total_scheduled_cdp)
        delta_occ_s3 = f'{self.s3_service.get_base_path()}/{get_s3_path_calibration(client_code, property_code, caught_up_date)}/deltaOcc.csv'

        self.sns_service.send_trigger_ac_sold_pace_event(client_code, property_code, property_id,
                                                         g3_base_call_url, start_bde_date,
                                                         start_bde_date + timedelta(days=900), caught_up_date,
                                                         delta_occ_s3)

    @timeit()
    def refresh_calibration_delta_lrv_pace(self, caught_up_date, client_code, property_code, start_bde_date):
        if self.use_any_api():
            delta_lrv_pace_df = self.g3_any_api_service.fetch(DeltaLrvPaceRequest(start_bde_date, caught_up_date))
            delta_lrv_pace_df['captureDate'] = (pd.to_datetime(delta_lrv_pace_df['occupancyDate']) - pd.to_timedelta(
                delta_lrv_pace_df['leadTime'], unit='days')).dt.strftime('%Y-%m-%d')
        else:
            delta_lrv_pace = self.g3_data_pull_service.fetch_delta_lrv_pace_for_calibration(start_bde_date,
                                                                                            caught_up_date)
            delta_lrv_pace_df = to_df(list[DeltaLRVPace], delta_lrv_pace)
        self.s3_service.upload_df(
            dest_path=f'{get_s3_path_calibration(client_code, property_code, caught_up_date)}/{DELTA_LRV_PACE_FILE_NAME}',
            df=delta_lrv_pace_df)

    def use_any_api(self):
        return get_bool_value(EnvironmentVariable.USE_G3_ANY_API, True)

    @timeit()
    def refresh_calibration_reference_rate(self, caught_up_date, client_code, property_code, start_bde_date):
        prices = self.g3_data_pull_service.fetch_reference_rate_pace_for_calibration(start_bde_date, caught_up_date)
        self.s3_service.upload_df(
            dest_path=f'{get_s3_path_calibration(client_code, property_code, caught_up_date)}/{REFERENCE_PRICE_PACE_FILE_NAME}',
            df=to_df(list[ReferencePricePace], prices))

    def refresh_evaluation_data(self, caught_up_date, client_code, property_code):
        recent_delta_lrv = self.g3_data_pull_service.fetch_recent_lrv(caught_up_date)
        recent_prices = self.g3_data_pull_service.fetch_recent_reference_rate(caught_up_date)
        capacity: list[AccomAvailableCapacity] = self.g3_any_api_service.fetch_as_type(AccomAvailableCapacityRequest(caught_up_date),
                                                                                              list[AccomAvailableCapacity])
        self.dynamo_service.update_delta_lrv_latest(recent_delta_lrv, client_code, property_code)
        self.dynamo_service.update_ref_price_latest(recent_prices, client_code, property_code)
        self.dynamo_service.update_available_capacity(capacity, client_code, property_code)

    def refresh_evaluation_data_store_lrv_change(self, caught_up_date, client_code, property_code):
        new_lrv_decision = self.g3_data_pull_service.fetch_recent_lrv(caught_up_date)
        recent_prices = self.g3_data_pull_service.fetch_recent_reference_rate(caught_up_date)
        abs_change = self.__compute_decision_lrv_change(client_code, new_lrv_decision, property_code, caught_up_date)
        capacity: list[AccomAvailableCapacity] = self.g3_any_api_service.fetch_as_type(AccomAvailableCapacityRequest(caught_up_date),
                                                                                       list[AccomAvailableCapacity])
        self.dynamo_service.update_decision_change(abs_change, client_code, property_code, caught_up_date)
        self.dynamo_service.update_delta_lrv_latest(new_lrv_decision, client_code, property_code)
        self.dynamo_service.update_ref_price_latest(recent_prices, client_code, property_code)
        self.dynamo_service.update_available_capacity(capacity, client_code, property_code)

    def __compute_decision_lrv_change(self, client_code, new_lrv_decision, property_code, caught_up_date):
        existing_lrv_decision = (self.dynamo_service.get_current_lrv(client_code, property_code)
                                 .map(lambda x: float(x) if isinstance(x, Decimal) else x))
        if existing_lrv_decision.empty:
            return 0
        max_occ_date = pd.to_datetime(self.g3_any_api_service.fetch(MaxLrvDecisionOccDate(caught_up_date)).iloc[0]['maxOccDate'])
        new_lrv_decision_df = (to_df(list[DeltaLRVEvaluation], new_lrv_decision)
                               .map(lambda x: float(x) if isinstance(x, Decimal) else x))
        new_lrv_decision_df['occupancyDate'] = pd.to_datetime(new_lrv_decision_df['occupancyDate'])
        new_lrv_decision_df = new_lrv_decision_df.query('occupancyDate <= @max_occ_date')
        existing_lrv_decision['occupancyDate'] = pd.to_datetime(existing_lrv_decision['occupancyDate'])
        new_lrv_decision_df = new_lrv_decision_df.set_index(['accomClassId', 'occupancyDate', 'leadTime'])
        existing_lrv_decision = existing_lrv_decision.set_index(['accomClassId', 'occupancyDate', 'leadTime'])
        abs_change = np.abs(new_lrv_decision_df['lrv'].sub(existing_lrv_decision['lrv'])
                            .astype(float).dropna()).median()
        
        if pd.isnull(abs_change) or np.isinf(abs_change):
            self.logger.debug("Absolute LRV change is invalid, defaulting to 0")
            return 0

        return abs_change


class DataRefreshServiceFactory:

    @staticmethod
    def from_client_property_codes_base_url(client_code, property_code, base_url, property_id) -> DataRefreshService:
        g3_data_pull_service = G3DataPullServiceFactory.init(base_url, client_code, property_code)
        return DataRefreshService(g3_data_pull_service, DynamoDBService(), S3Service(),
                                  SNSService(),
                                  G3AnyApiService(HttpService(base_url, authorizer=G3Auth()), property_id))

    @staticmethod
    def from_services(g3_data_pull_service: G3DataPullService, dynamodb_service: DynamoDBService, s3_service: S3Service,
                      sns_service: SNSService, g3_any_api_service) -> DataRefreshService:
        return DataRefreshService(g3_data_pull_service, dynamodb_service, s3_service,
                                  sns_service, g3_any_api_service)
