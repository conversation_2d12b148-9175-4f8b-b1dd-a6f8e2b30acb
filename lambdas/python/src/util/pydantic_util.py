from typing import TypeVar

from pydantic import TypeAdapter, BaseModel

T = TypeVar('T', bound=BaseModel)


def from_json(model: type[T], data: str) -> T:
    return TypeAdapter(model).validate_json(data)


def from_dict_array(model: type[list[T]], data: list[dict]) -> list[T]:
    return TypeAdapter(model).validate_python(data)


def from_dict(model: type[T], data: dict) -> T:
    return TypeAdapter(model).validate_python(data)


def to_json(model: type[T], data: T) -> bytes:
    return TypeAdapter(model).dump_json(data)


def to_json_string(model: T, by_alias=True) -> str:
    return model.model_dump_json(by_alias=by_alias)


def to_dict(model: type[T], data: T) -> bytes:
    return TypeAdapter(model).dump_python(data)

def to_dict_array(model: type[list[T]], data: list[T]) -> list[dict]:
    return TypeAdapter(model).dump_python(data, by_alias=True)


def to_json_array(model: type[list[T]], data: list[T]) -> bytes:
    return TypeAdapter(model).dump_json(data)
