resource "aws_iam_policy" "dynamodb_table_access" {
  name = "dynamodb-table-access"
  policy = jsonencode(
    {
      "Version" : "2012-10-17",
      "Statement" : [
        {
          "Effect" : "Allow",
          "Action" : [
            "dynamodb:BatchGetItem",
            "dynamodb:BatchWriteItem",
            "dynamodb:ConditionCheckItem",
            "dynamodb:PutItem",
            "dynamodb:DescribeTable",
            "dynamodb:DeleteItem",
            "dynamodb:GetItem",
            "dynamodb:Scan",
            "dynamodb:Query",
            "dynamodb:UpdateItem"
          ],
          "Resource" : [
            module.dyn-opt-accom-type-mapping.dynamodb_table_arn,
            module.dyn_opt_delta_lrv_latest.dynamodb_table_arn,
            module.dyn_opt_ref_price_latest.dynamodb_table_arn,
            module.dyn_opt_property_incremental_solds.dynamodb_table_arn,
            module.dyn_opt_calibrated_potentials.dynamodb_table_arn,
            module.dyn_opt_idp_count.dynamodb_table_arn,
            module.dyn_opt_idp_window.dynamodb_table_arn,
            module.dyn_opt_idp_window_by_roomclass.dynamodb_table_arn,
            module.dyn_opt_property_last_incremental_time.dynamodb_table_arn,
            module.dyn_opt_calib_date.dynamodb_table_arn,
            module.dyn_opt_decision_change.dynamodb_table_arn,
            module.dyn_opt_available_capacity.dynamodb_table_arn
          ]
        }
      ]
    }
  )
}


resource "aws_iam_policy" "lambda_access" {
  name = "lambda-access"
  policy = jsonencode(
    {
      "Version" : "2012-10-17",
      "Statement" : [
        {
          "Effect" : "Allow",
          "Action" : [
            "lambda:GetAlias",
            "lambda:GetFunction",
            "lambda:CreateFunction",
            "lambda:CreateAlias",
            "lambda:UpdateAlias",
            "lambda:DeleteAlias",
            "lambda:UpdateFunctionCode",
            "lambda:UpdateFunctionConfiguration",
            "lambda:ListVersionsByFunction",
            "lambda:PublishVersion",
            "lambda:GetFunctionConfiguration",
            "lambda:InvokeFunction",
            "lambda:DeleteFunction"
          ],
          "Resource" : [
            "arn:aws:lambda:*:${local.account_id}:function:dyn-opt*"
          ]
        }
      ]
    }
  )
}

resource "aws_iam_role_policy_attachment" "evaluation_check_attachment" {
  role       = module.evaluation_check.codedeploy_iam_role_name
  policy_arn = aws_iam_policy.lambda_access.arn
}

resource "aws_iam_role_policy_attachment" "data_collector_attachment" {
  role       = module.data_collector.codedeploy_iam_role_name
  policy_arn = aws_iam_policy.lambda_access.arn
}

resource "aws_iam_role_policy_attachment" "solds_drift_handler_attachment" {
  role       = module.solds_drift_handler.codedeploy_iam_role_name
  policy_arn = aws_iam_policy.lambda_access.arn
}

#S3Access
#tfsec:ignore:aws-iam-no-policy-wildcards
resource "aws_iam_policy" "s3_access_to_analytics_service" {
  name = "s3AccessToAnalyticsService"
  policy = jsonencode(
    {
      "Version" : "2012-10-17",
      "Statement" : [
        {
          "Effect" : "Allow",
          "Action" : [
            "s3:*Object"
          ],
          "Resource" : "${module.dyn_opt_data_s3.s3_bucket_arn}/*"
        }
      ]
    }
  )
}


resource "aws_iam_role_policy_attachment" "dynamodb_access_to_analytics" {
  policy_arn = aws_iam_policy.dynamodb_table_access.arn
  role       = local.analytics_ecs_role_name
}
resource "aws_iam_role_policy_attachment" "s3_access_to_analytics" {
  policy_arn = aws_iam_policy.s3_access_to_analytics_service.arn
  role       = local.analytics_ecs_role_name
}

resource "aws_iam_policy" "secret_value_access" {
  name = "dyn-opt-secret-value-access-policy"
  policy = jsonencode(
    {
      "Version" : "2012-10-17",
      "Statement" : [
        {
          "Effect" : "Allow",
          "Action" : [
            "secretsmanager:GetSecretValue",
          ],
          "Resource" : [aws_secretsmanager_secret.rdl_analytic_secret.arn]
        }
      ]
    }
  )
}