#!/bin/bash


# Create SQS queues
echo "Creating dynamodb table"
awslocal dynamodb create-table \
    --table-name MyTable \
    --attribute-definitions AttributeName=clientCode_propertyCode,AttributeType=S AttributeName=accomType,AttributeType=S \
    --key-schema AttributeName=clientCode_propertyCode,KeyType=HASH AttributeName=accomType,KeyType=RANGE \
    --on-demand-throughput MaxReadRequestUnits=100,MaxWriteRequestUnits=100 \
    --region us-east-2 \
    --billing-mode PAY_PER_REQUEST

echo "dynamodb table created."

echo "Creating sqs queue."
awslocal create-queue  --queue-name do-updates --region us-east-2
echo "sqs table created."


#Create SNS
echo "Creating a SNS Topic"
awslocal sns create-topic --name fds-sns --region us-east-2
awslocal sns subscribe --topic-arn arn:aws:sns:us-east-2:000000000000:fds-sns --protocol sqs \
        --notification-endpoint  arn:aws:sqs:us-east-2:000000000000:do-updates --region us-east-2
echo "Done creating sns topic and adding subscription"

# Creating a S3 Storage bucket
echo "creating a S3 Bucket"
awslocal s3 mb s3://dynamic-optimization-calibration --region us-east-2
echo "done creating a S3 bucket"


