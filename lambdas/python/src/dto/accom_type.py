from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

from src.dto.accom_class import AccomClass


class AccomType(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: int
    name: str
    accom_type_code: str
    accom_class: AccomClass
    status_id: int
    system_default: int

    def is_active(self):
        return self.status_id == 1 and self.system_default == 0

    def dynamodb_record(self, client_code, property_code):
        return {
            'clientCode': client_code,
            'propertyCode': property_code,
            'accomType': self.accom_type_code,
            'accomClassId': self.accom_class.id,
            'clientCode_propertyCode': self.get_partition_key(client_code, property_code)
        }


    @staticmethod
    def get_partition_key(client_code, property_code):
        return f'{client_code}_{property_code}'
