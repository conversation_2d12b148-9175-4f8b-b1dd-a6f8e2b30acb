from datetime import date, datetime
from unittest.mock import patch, MagicMock

import pandas as pd
import pytest

from src.events.significant_solds_drift_detected_event import SignificantSoldsDriftDetectedEvent
from src.handler.significant_solds_drift_detected_handler import SignificantSoldsDriftHandler


class TestSignificantSoldsDriftDetectedHandler:
    NOW = datetime(2024, 1, 1,1, 1,1,12345)
    TODAY = date(2024,10,10)
    EVENT = SignificantSoldsDriftDetectedEvent(
        client_code='Hilton',
        property_code='ABILN',
        max_occupancy_date=TODAY,
        caught_up_date=TODAY,
        evaluation_time=NOW,
        rc_to_occupancy_date_mapping={'7': '2025-03-04','8': '2025-03-05'}
    )

    @patch('src.handler.significant_solds_drift_detected_handler.S3Service.upload_df')
    @patch('src.handler.significant_solds_drift_detected_handler.DynamoDBService')
    def test_handle_when_event_is_valid(self, mock_dynamodb_service, upload_df: MagicMock):
        mock_service_instance = mock_dynamodb_service.return_value
        mock_service_instance.truncate_inc_solds.return_value = [{'a': 1, 'b': 2}, {'a': 4, 'b': 6}]
        upload_df.return_value = None
        handler = SignificantSoldsDriftHandler()
        handler.handle(self.EVENT)

        assert upload_df.call_args_list[0].args[0] == 'Hilton/ABILN/2024_10_10/2024_01_01T01_01_01_012345_inc_solds.csv'
        pd.testing.assert_frame_equal(pd.DataFrame({'a': [1, 4], 'b': [2, 6]}), upload_df.call_args_list[0].args[1])
        mock_service_instance.truncate_inc_solds.assert_called_once_with('Hilton', 'ABILN')
        mock_service_instance.increment_idp_count.assert_called_once_with('Hilton', 'ABILN', self.TODAY)
        mock_service_instance.save_idp_window.assert_called_once_with('Hilton', 'ABILN', self.EVENT.evaluation_time, self.TODAY, self.TODAY)
        mock_service_instance.save_idp_window_by_roomclass.assert_called_once_with('Hilton', 'ABILN', self.EVENT.evaluation_time, self.TODAY, {'7': '2025-03-04','8': '2025-03-05'})

    @patch('src.handler.significant_solds_drift_detected_handler.DynamoDBService')
    def test_handle_invalid_event_type(self, mock_dynamodb_service):
        invalid_event = MagicMock()
        handler = SignificantSoldsDriftHandler()

        with pytest.raises(AssertionError, match="event is not SignificantSoldsDriftDetected"):
            handler.handle(invalid_event)

        mock_service_instance = mock_dynamodb_service.return_value
        mock_service_instance.truncate_inc_solds.assert_not_called()
        mock_service_instance.increment_idp_count.assert_not_called()
        mock_service_instance.save_idp_window.assert_not_called()
        mock_service_instance.save_idp_window_by_roomclass.assert_not_called()
