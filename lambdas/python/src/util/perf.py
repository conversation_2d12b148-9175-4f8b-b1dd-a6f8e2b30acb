import functools
import logging
import os
from time import perf_counter
from typing import Callable, Any

import psutil

logger = logging.getLogger(__name__)


def __get_process_memory():
    process = psutil.Process(os.getpid())
    mem_info = process.memory_info()
    return mem_info.rss


def timeit(tag: str = ""):
    def timer(func: Callable[..., Any]) -> Callable[..., Any]:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            cur_memory = __get_process_memory()
            start = perf_counter()
            result = func(*args, **kwargs)
            end = perf_counter()
            after_memory = __get_process_memory()
            mem_usage = (after_memory - cur_memory) / (1024 * 1024)
            logger.info(f"{tag} {func.__name__} took {end - start}s and {mem_usage} bytes")
            return result
        return wrapper
    return timer
