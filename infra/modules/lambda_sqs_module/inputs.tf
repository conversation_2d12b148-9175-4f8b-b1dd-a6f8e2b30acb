variable "lambda" {
  type = object({
    name                              = string
    description                       = optional(string, "")
    path                              = string
    handler                           = string
    artifact                          = string
    runtime                           = string
    timeout                           = optional(number, 120)
    memory_size                       = optional(number, 512)
    provisioned_concurrent_executions = optional(number, -1)
    reserved_concurrent_executions    = optional(number, -1)
    environment_variables             = optional(map(string), {})
    custom_policies                   = optional(list(string), [])
    tracing_mode                      = optional(string, "Active")
    vpc                               = optional(bool, false)
    vpc_subnet_ids                    = optional(list(string), [])
    vpc_security_group_ids            = optional(list(string), [])
    layers                            = optional(list(string), [])
    alias_name                        = optional(string, "live")
    deployment_config_name            = optional(string, "CodeDeployDefault.LambdaAllAtOnce")
    sqs_mapping = optional(object({
      maximum_batching_window_in_seconds = number
      batch_size                         = number
      }),
      {
        maximum_batching_window_in_seconds = 5
        batch_size                         = 3
      }
    )
  })
  description = "Lambda configuration"
}

variable "sqs" {
  type = object({
    queue_policy_statements        = optional(map(any), {})
    fifo_queue                     = optional(bool, false)
    content_based_deduplication    = optional(bool, false)
    visibility_timeout_seconds     = optional(number)
    visibility_timeout_add_seconds = optional(number, 10) # add N seconds to the lambda timeout
    redrive_policy = optional(object({
      maxReceiveCount = number
      }),
      {
        maxReceiveCount = 5
      }
    )
    create_dlq              = optional(bool, true)
    sqs_managed_sse_enabled = optional(bool, true)
  })

  description = "SQS configuration"
  default     = {}
}

variable "tags" {
  type        = map(string)
  description = "A map of tags to assign to resources."
  default     = {}
}
