from datetime import date, timedelta

from pydantic import BaseModel, ConfigDict, computed_field
from pydantic.alias_generators import to_camel


class AccomAvailableCapacity(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    accom_class_id: int
    occupancy_date: date
    available_capacity: int
    lead_time: int
    capture_date: date

    def dynamodb_record(self, client_code, property_code):
        return {
            'clientCode_propertyCode': self.get_partition_key(client_code, property_code),
            'accomClassId_occupancyDate': f'{self.accom_class_id}_{self.occupancy_date.isoformat()}',
            'clientCode': client_code,
            'propertyCode': property_code,
            'accomClassId': self.accom_class_id,
            'occupancyDate': self.occupancy_date.isoformat(),
            'leadTime': self.lead_time,
            'captureDate': self.capture_date.isoformat(),
            'availableCapacity': self.available_capacity
        }

    @staticmethod
    def get_partition_key(client_code, property_code):
        return f'{client_code}_{property_code}'