from io import Bytes<PERSON>
from unittest.mock import patch, MagicMock, call

import pandas as pd

from src.service.s3_service import S3Service


class TestS3Service:
    service = S3Service()

    @patch('src.service.s3_service.s3')
    def test_upload_df(self, s3_mock: MagicMock) -> None:
        frame = pd.DataFrame()
        s3_mock.to_csv.return_value = None
        self.service.upload_df(dest_path='path', df=frame)

        assert s3_mock.to_csv.call_args_list == [call(frame, path='s3://S3_BUCKET_NAME/path', index=False)]

    @patch('src.service.s3_service.s3')
    def test_upload(self, s3_mock: MagicMock) -> None:
        s3_mock.upload.return_value = None
        self.service.upload(bytes('str', 'utf-8'), dest_path='path')

        class BytesCompare(object):
            def __eq__(self, other):
                return isinstance(other, BytesIO)

        assert s3_mock.upload.call_args_list == [call(BytesCompare(), 's3://S3_BUCKET_NAME/path')]
