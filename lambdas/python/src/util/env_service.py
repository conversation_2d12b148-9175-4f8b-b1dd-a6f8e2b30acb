import os
from typing import Callable

from src.enums.env_var import EnvironmentVariable


def get_value(var: EnvironmentVariable, default_value):
    return os.getenv(var.value, default_value)


def get_bool_value(var: EnvironmentVariable, default_value=False) -> bool:
    return str(get_value(var, default_value)).lower() in ['true', '1']


def get_int_value(var: EnvironmentVariable, default_value: int) -> int:
    return int(get_value(var, default_value))

def get_value_or_default(var: EnvironmentVariable, default_value_supplier: Callable):
    env_value = os.getenv(var.value)
    return env_value if env_value is not None else default_value_supplier()
