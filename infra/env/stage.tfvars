environment = "stage"
fds_uen_arn = "arn:aws:sns:us-east-2:094974311374:fds-unified-event-notification-topic"

us_east_2_vpc_id = "vpc-0e96469da22f075f7"

us_east_2a_private_subnet_id = "subnet-001e9948cc06a2cd5"
us_east_2b_private_subnet_id = "subnet-0de65de9b3c53aae9"
us_east_2c_private_subnet_id = "subnet-08573ff99d95709e4"

integration_setting_url = "https://integration-setting-internal.stage.ideasrms.com"

eval_time_interval = 720

secret_manager_name = "dyn-opt-secret"

uis_base_url = "https://fds.stage.ideasrms.com/api/uis"
ucs_base_url = "https://fds.stage.ideasrms.com/api/ucs"
g3_base_url  = "https://mn4qg3xenvl004.ideasdev.int"