from datetime import date
from typing import Optional

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

from src.dto.event_source_body import EventSourceBody


class Drift(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    incrementalSoldsByOccupancyDate: dict[date, int]
    market_segment: str
    rate_code: Optional[str] = ""
    reservation_id: str
    room_type_code: str


class SoldsDriftDetectedEvent(EventSourceBody):

    property_code: str
    client_code: str
    drift: Drift
    blocked: bool

