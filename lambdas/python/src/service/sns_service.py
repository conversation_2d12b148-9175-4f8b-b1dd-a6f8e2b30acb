import logging

import boto3

from src.dto.evaluation_dynamodb_input import EvaluationDynamodbTableInput
from src.dto.idp_count import IdpCount
from src.dto.sns import SNSEvent
from src.enums.env_var import EnvironmentVariable
from src.events.outgoing.dyn_opt_evaluate_for_idp import DynOptEvaluateForIdp
from src.events.outgoing.dyn_opt_potential_calibration import DynOptPotentialCalibration
from src.events.outgoing.dyn_opt_potential_revision import DynOptPotentialRevision
from src.events.outgoing.trigger_ft_ac_report import GenerateFTACReport, ReportContext
from src.util.env_service import get_value
from src.util.pydantic_util import to_json_string


class SNSService:
    GENERATE_AC_PACE = 'GENERATE_AC_PACE'
    DYN_OPT_POTENTIAL_CALIBRATION = 'DYN_OPT_POTENTIAL_CALIBRATION'
    DYN_OPT_POTENTIAL_REVISION = 'DYN_OPT_REVISE_POTENTIAL'
    DYN_OPT_IDP_COUNT = 'DYN_OPT_IDP_COUNT'
    DYN_OPT_EVALUATE_FOR_IDP = 'DYN_OPT_EVALUATE_FOR_IDP'

    def __init__(self, sns_client=boto3.client('sns',
                                               endpoint_url=get_value(EnvironmentVariable.AWS_SNS_ENDPOINT_URL, None))):
        self.sns_client = sns_client
        self.topic_arn = get_value(EnvironmentVariable.FDS_SNS_TOPIC_ARN, 'FDS_TOPIC_ARN')
        self.env = get_value(EnvironmentVariable.ENV, 'prod')
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")

    def send_trigger_ac_sold_pace_event(self, client_code, property_code, property_id, base_call_back_url, start_date,
                                        end_date, caught_up_date, s3_path: str):
        event_source = GenerateFTACReport(base_callback_U_R_L=base_call_back_url, property_id=property_id,
                                          client_code=client_code,
                                          property_code=property_code,
                                          report_context=ReportContext(start_date=start_date, end_date=end_date),
                                          caught_up_date=caught_up_date,
                                          destination_s3_path=s3_path)
        self.send_event(event_source, self.GENERATE_AC_PACE)

    def send_idp_count_value_event(self, client_code, property_code, count, caught_up_date):
        event_source = IdpCount(client_code=client_code, property_code=property_code, count=count, caught_up_date=caught_up_date)
        self.send_event(event_source, self.DYN_OPT_IDP_COUNT)

    def send_dyn_opt_potential_calibration_request(self, client_code, property_code, calibration_s3_file_input):
        event_source = DynOptPotentialCalibration(client_code=client_code, property_code=property_code, request_context=calibration_s3_file_input,
                                                  destination_table=get_value(EnvironmentVariable.CALIBRATED_POTENTIAL_TABLE, "table"))
        self.send_event(event_source, self.DYN_OPT_POTENTIAL_CALIBRATION)

    def send_dyn_opt_potential_revision_request(self, client_code, property_code):
        event_source = DynOptPotentialRevision(client_code=client_code, property_code=property_code)
        self.send_event(event_source, self.DYN_OPT_POTENTIAL_REVISION)

    def send_evaluate_idp_request(self, client_code, property_code, evaluation_dynamodb_input: EvaluationDynamodbTableInput):
        event_source = DynOptEvaluateForIdp(client_code=client_code, property_code=property_code, request_context=evaluation_dynamodb_input)
        self.send_event(event_source, self.DYN_OPT_EVALUATE_FOR_IDP)

    def send_event(self, event_source, event_name):
        sns_event = SNSEvent(scope=self.env, event_type=event_name, event_source=to_json_string(event_source))
        self.logger.info(f"Sent SNS Event : {sns_event}")
        self.sns_client.publish(
            TopicArn=self.topic_arn,
            Message=to_json_string(sns_event),
            MessageAttributes=sns_event.get_message_attributes()
        )


sns_service = SNSService()
