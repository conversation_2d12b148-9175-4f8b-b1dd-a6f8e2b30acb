import json

from awswrangler import dynamodb

from src.sqs_trigger import lambda_handler


def run_event():
    event_source = json.dumps(
        {'caughtUpDate': '2024-01-01', 'baseCallbackURL': 'http://mn4qg3xenvw102.ideasdev.int', 'propertyId': 993015,
         'clientCode': 'TRAVELODGE',
         'propertyCode': '0617'})
    event = {
        'Records': [
            {
                'body': json.dumps({'sourceSystem': 'local',
                                    'eventType': 'ACCOMODATION_CONFIG_CHANGE',
                                    'version': 1,
                                    'eventDateTime': '2023-02-28T00:00:00Z',
                                    'eventSource': event_source
                                    })
            }
        ]
    }

    lambda_handler(event, None)


def validate_data():
    saved_ats = dynamodb.read_items(table_name='MyTable', allow_full_scan=True, as_dataframe=False)
    expected_ats = [
        {'propertyCode': '0617', 'clientCode_propertyCode': 'TRAVELODGE_0617', 'accomType': 'DHZN', 'accomClass': 'STD',
         'clientCode': 'TRAVELODGE'},
        {'propertyCode': '0617', 'clientCode_propertyCode': 'TRAVELODGE_0617', 'accomType': 'DN', 'accomClass': 'STD',
         'clientCode': 'TRAVELODGE'},
        {'propertyCode': '0617', 'clientCode_propertyCode': 'TRAVELODGE_0617', 'accomType': 'DSPN', 'accomClass': 'FAM',
         'clientCode': 'TRAVELODGE'}]

    assert saved_ats == expected_ats

if __name__ == '__main__':
    run_event()
    validate_data()
