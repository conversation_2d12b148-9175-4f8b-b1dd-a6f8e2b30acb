from datetime import datetime

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

from src.exceptions.ucs_exception import UCSException


class UCSParam(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)
    config_param_name: str
    default_value: str
    type: str
    value: str

    def get_typed_value(self):
        type_cast_by_type = {
            'boolean': lambda x: x.lower() == 'true',
            'numeric': lambda x: float(x) if '.' in x else int(x),
            'datetime': lambda x: datetime.fromisoformat(x)
        }
        if self.type.lower() in type_cast_by_type:
            return type_cast_by_type[self.type.lower()](self.value)

        raise UCSException(f"Unexpected type {self}")