from datetime import date
from unittest.mock import MagicMock, call

from src.dto.sns import SNS<PERSON>vent
from src.events.outgoing.trigger_ft_ac_report import GenerateFTACReport, ReportContext
from src.service.sns_service import SNSService
from src.util.pydantic_util import to_json_string


class TestSNSService:
    __CLIENT_CODE = 'client_code'
    __PROPERTY_CODE = 'property_code'
    __today = date.today()
    __g3_base_url = 'url'

    def test_send_trigger_fg_ac_event(self):
        mock = MagicMock()
        service = SNSService(sns_client=mock)
        event_source = GenerateFTACReport(base_callback_U_R_L=self.__g3_base_url, property_id=120,
                                          client_code=self.__CLIENT_CODE, property_code=self.__PROPERTY_CODE,
                                          report_context=ReportContext(start_date=self.__today, end_date=self.__today),
                                          caught_up_date=self.__today, destination_s3_path='path')
        service.sns_client.publish.return_value = None
        to_send = SNSEvent(scope='prod', event_type=service.GENERATE_AC_PACE, event_source=to_json_string(event_source))
        service.send_trigger_ac_sold_pace_event(self.__CLIENT_CODE, self.__PROPERTY_CODE, 120, self.__g3_base_url, self.__today, self.__today, self.__today, 'path')

        assert service.sns_client.publish.call_args_list == [call(TopicArn='FDS_TOPIC_ARN', Message=to_json_string(to_send),
                                                                  MessageAttributes=to_send.get_message_attributes())]
