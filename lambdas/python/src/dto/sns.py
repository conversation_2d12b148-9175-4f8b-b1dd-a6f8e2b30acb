from datetime import datetime, timezone

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel


class SNSEvent(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    source_system: str = "dyn-opt"
    scope: str
    version: str = "1"
    event_date_time: datetime | str = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
    event_source: str
    event_type: str

    # def __init__(self, scope, event, event_source):
    #     current_time_utc = datetime.now(timezone.utc)
    #     time_string = current_time_utc.strftime("%Y-%m-%dT%H:%M:%SZ")
    #     self.source_system = "dyn-opt"
    #     self.event_type = "GENERATE_G3_MONITORING_REPORT_COMPLETED"
    #     self.scope = scope
    #     self.version = "1"
    #     self.event_datetime = time_string
    #     self._event_source = event_source
    #
    # @property
    # def event_source(self) -> Dict[str, Any]:
    #     return self._event_source
    #
    # @event_source.setter
    # def event_source(self, value: Dict[str, Any]):
    #     self._event_source = value

    # def to_dict(self) -> Dict[str, Any]:
    #     return {
    #         "sourceSystem": self.source_system,
    #         "eventType": self.event_type,
    #         "scope": self.scope,
    #         "version": self.version,
    #         "eventDateTime": self.event_datetime,
    #         "eventSource": json.dumps(self._event_source)
    #     }

    def get_message_attributes(self):
        return {"sourceSystem": {"DataType": "String", "StringValue": self.source_system},
                "version": {"DataType": "String", "StringValue": self.version},
                "eventType": {"DataType": "String", "StringValue": self.event_type},
                "scope": {"DataType": "String", "StringValue": self.scope}}
