from datetime import date

from src.dto.accom_type import AccomType
from src.dto.delta_lrv import DeltaLRV
from src.dto.delta_lrv_evaluation import DeltaLRVEvaluation
from src.dto.delta_lrv_pace import DeltaLRVPace
from src.dto.reference_price_pace import ReferencePricePace
from src.enums.env_var import EnvironmentVariable
from src.events.significant_solds_drift_detected_event import SignificantSoldsDriftDetectedEvent
from src.service.http_service import HttpService
from src.util.env_service import get_value
from src.util.g3_auth import G3Auth


class G3DataPullService:
    ACCOM_TYPE_MAPPING_PATH = 'api/accom/roomTypes/v1'
    CALIBRATION_DELTA_LRV_PACE_DATA = 'api/data/analytics/dynamic-optimization/deltalrv/v1/{startBDEDate}/{endBDEDate}'
    CALIBRATION_REF_PRICE_PACE_DATA = 'api/data/analytics/dynamic-optimization/reference-price/bar/v1/{startBDEDate}/{endBDEDate}'
    RECENT_DELTA_LRV = 'api/decision/summary/lrv/between/{occStartDate}/{occEndDate}/v1'
    TRIGGER_ON_DEMAND_IDP = 'api/ondemand-idp/start'

    def __init__(self, g3_http_service: HttpService, client_code: str, property_code: str,
                 page_size=int(get_value(EnvironmentVariable.DEFAULT_PAGE_SIZE, '28000'))):
        self.g3 = g3_http_service
        self.client_property_code_query_params: dict = {
            'clientCode': client_code, 'propertyCode': property_code
        }
        self.page_size = page_size

    def fetch_accom_types(self) -> list[AccomType]:
        ats: list[AccomType] = self.g3.get(self.ACCOM_TYPE_MAPPING_PATH, return_type=list[AccomType],
                                           params=self.client_property_code_query_params)

        return [a for a in ats if a.is_active() is True]

    def fetch_delta_lrv_pace_for_calibration(self, start_bde_date: date, end_bde_date: date) -> list[DeltaLRVPace]:
        url_specific_params = {'startBDEDate': start_bde_date, 'endBDEDate': end_bde_date}
        url_specific_params.update(self.client_property_code_query_params)
        return self.g3.get_paged_api(self.CALIBRATION_DELTA_LRV_PACE_DATA, return_type=list[DeltaLRVPace],
                                     page_size=self.page_size, params=url_specific_params)

    def fetch_reference_rate_pace_for_calibration(self, start_bde_date, end_bde_date) -> list[ReferencePricePace]:
        url_specific_params = {'startBDEDate': start_bde_date, 'endBDEDate': end_bde_date}
        url_specific_params.update(self.client_property_code_query_params)
        return self.g3.get_paged_api(self.CALIBRATION_REF_PRICE_PACE_DATA, return_type=list[ReferencePricePace],
                                     page_size=self.page_size, params=url_specific_params)

    def fetch_recent_lrv(self, caught_up_date: date) -> list[DeltaLRVEvaluation]:
        delta_lrv = self.g3.get_paged_api(
            self.RECENT_DELTA_LRV.format(occStartDate=caught_up_date, occEndDate=date.max), return_type=list[DeltaLRV],
            page_size=self.page_size, params=self.client_property_code_query_params)
        return [DeltaLRVEvaluation.from_delta_lrv(d, caught_up_date) for d in delta_lrv]

    def fetch_recent_reference_rate(self, caught_up_date: date):
        return self.fetch_reference_rate_pace_for_calibration(caught_up_date, caught_up_date)

    def trigger_on_demand_idp(self, event: SignificantSoldsDriftDetectedEvent):
        params = {
            "nextIdpWindow": (event.max_occupancy_date - event.caught_up_date).days
        }
        params.update(self.client_property_code_query_params)
        self.g3.post_with_params(path=self.TRIGGER_ON_DEMAND_IDP, params=params)


class G3DataPullServiceFactory:

    @staticmethod
    def init(base_url: str, client_code: str, property_code: str) -> G3DataPullService:
        return G3DataPullService(g3_http_service=HttpService(base_url=base_url, authorizer=G3Auth()),
                                 client_code=client_code, property_code=property_code)
