import logging
from datetime import timed<PERSON><PERSON>, date
from typing import override

from src.abc.event_handler import <PERSON><PERSON>andler
from src.events.bde_completed_event import BDECompletedEvent
from src.handler.potential_calibration_handler import PotentialCalibrationHandler
from src.log_config.setup import setup_logger
from src.service.data_refresh_service import DataRefreshServiceFactory
from src.service.dynamodb_service import dynamodb_service
from src.service.integration_setting_service import IntegrationSettingService
from src.service.sns_service import sns_service

POTENTIAL_RECALIB_DAYS = 28
POTENTIAL_REVISION_DAYS = 7


class BDECompletedHandler(EventHandler):
    logger = logging.getLogger(__name__)

    @override
    def handle(self, event: BDECompletedEvent):
        assert isinstance(event, BDECompletedEvent), "event is not BDECompletedEvent"
        setting_service: IntegrationSettingService = IntegrationSettingService.from_int_set_url()
        if not setting_service.is_dynamic_optimization_enabled(event.client_code, event.property_code):
            self.logger.debug("Skipping BDE completed event")
            return
        dynamodb_service.update_last_optimization_time(client_code=event.client_code, property_code=event.property_code)
        data_refresh_service = DataRefreshServiceFactory.from_client_property_codes_base_url(event.client_code,
                                                                                             event.property_code,
                                                                                             event.base_callback_u_r_l,
                                                                                             event.property_id)
        data_refresh_service.refresh_evaluation_data(event.caught_up_date, event.client_code, event.property_code)
        if dynamodb_service.fetch_potential_calib_date(event.client_code, event.property_code) < (event.caught_up_date - timedelta(days=POTENTIAL_RECALIB_DAYS)):
            PotentialCalibrationHandler().handle(event)
        elif dynamodb_service.fetch_potential_revision_date(event.client_code, event.property_code) < (event.caught_up_date - timedelta(days=POTENTIAL_REVISION_DAYS)):
            sns_service.send_dyn_opt_potential_revision_request(event.client_code, event.property_code)


if __name__ == '__main__':
    setup_logger()
    BDECompletedHandler().handle(BDECompletedEvent(base_callback_u_r_l='http://mn4qg3xenvl004.ideasdev.int',
                                                   client_code='Hilton', property_code='REKCU', property_id=993090,
                                                   caught_up_date=date.fromisoformat('2024-11-18')))
