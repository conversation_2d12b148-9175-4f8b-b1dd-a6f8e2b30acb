from typing import Self

from src.enums.env_var import EnvironmentVariable
from src.service.http_service import HttpService, HttpException
from src.util.env_service import get_value


class IntegrationSettingService:

    DYNAMIC_OPTIMIZATION_ENABLED_PATH = 'api/v1/integrationSettings/settings/value/{clientCode}/{propertyCode}/dynamicOptimizationEnabled'
    DYN_OPT_ENABLED_KEY = 'dynamicOptimizationEnabled'

    def __init__(self, http_service: HttpService):
        self.http_service = http_service

    @staticmethod
    def from_int_set_url() -> Self:
        return IntegrationSettingService(HttpService(get_value(EnvironmentVariable.INTEGRATION_SETTING_URL, "")))

    def is_dynamic_optimization_enabled(self, client_code: str , property_code: str) -> bool:
        if get_value(EnvironmentVariable.ENV, 'prod').lower() != 'prod':
            return True
        path = self.DYNAMIC_OPTIMIZATION_ENABLED_PATH.format(clientCode=client_code, propertyCode=property_code)
        try:
            resp: dict[str, str] = self.http_service.get_raw(path)
        except HttpException:
            return False
        return resp.get(self.DYN_OPT_ENABLED_KEY, "false").strip().lower() == "true"
