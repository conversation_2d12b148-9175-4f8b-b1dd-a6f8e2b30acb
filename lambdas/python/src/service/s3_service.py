from io import BytesIO

import pandas as pd
from awswrangler import s3

from src.enums.env_var import EnvironmentVariable
from src.util.env_service import get_value


class S3Service:

    def __init__(self, bucket_name: str = get_value(EnvironmentVariable.S3_NAME, 'S3_BUCKET_NAME')):
        self.bucket_name = bucket_name
        self.base_path = f"s3://{bucket_name}"

    def get_base_path(self):
        return self.base_path

    def upload_df(self, dest_path: str, df: pd.DataFrame):
        s3.to_csv(df, path=f"{self.base_path}/{dest_path}", index=False)

    def upload(self, bytes_to_upload, dest_path: str):
        s3.upload(BytesIO(bytes_to_upload), f"{self.base_path}/{dest_path}")
