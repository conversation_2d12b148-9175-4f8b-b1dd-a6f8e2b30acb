data "aws_vpc" "us_east_2_vpc" {
  id = var.us_east_2_vpc_id
}

data "aws_subnet" "private_us_east_2a" {
  id = var.us_east_2a_private_subnet_id
}

data "aws_subnet" "private_us_east_2b" {
  id = var.us_east_2b_private_subnet_id
}

data "aws_subnet" "private_us_east_2c" {
  id = var.us_east_2c_private_subnet_id
}

#tfsec:ignore:aws-ec2-no-public-egress-sgr
resource "aws_security_group" "lambda_can_access_the_internet" {
  vpc_id = data.aws_vpc.us_east_2_vpc.id
  name   = "${var.service_name}-lambda-can-access-the-internet"

  egress {
    from_port   = 0
    protocol    = "-1"
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }
}
