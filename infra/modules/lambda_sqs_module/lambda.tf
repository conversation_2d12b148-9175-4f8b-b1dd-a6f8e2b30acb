module "lambda" {
  source  = "terraform-aws-modules/lambda/aws"
  version = "~> 7.0"

  function_name = var.lambda.name
  description   = var.lambda.description
  handler       = var.lambda.handler
  runtime       = var.lambda.runtime
  timeout       = var.lambda.timeout
  memory_size   = var.lambda.memory_size

  provisioned_concurrent_executions = var.lambda.provisioned_concurrent_executions
  reserved_concurrent_executions    = var.lambda.reserved_concurrent_executions

  create_package         = false
  publish                = true
  local_existing_package = var.lambda.artifact

  environment_variables = var.lambda.environment_variables

  attach_policies    = length(var.lambda.custom_policies) == 0 ? false : true
  number_of_policies = length(var.lambda.custom_policies)
  policies           = var.lambda.custom_policies

  attach_policy_statements = true
  policy_statements = {
    sqs = {
      actions = [
        "sqs:ReceiveMessage",
        "sqs:DeleteMessage",
        "sqs:GetQueueAttributes"
      ]
      resources = [module.sqs_queue_for_lambda.queue_arn]
    }
  }

  tracing_mode                      = var.lambda.tracing_mode
  attach_tracing_policy             = var.lambda.tracing_mode != null ? true : false
  cloudwatch_logs_retention_in_days = 7

  vpc_subnet_ids         = var.lambda.vpc_subnet_ids
  vpc_security_group_ids = var.lambda.vpc_security_group_ids
  attach_network_policy  = var.lambda.vpc

  layers = var.lambda.layers

  tags = var.tags
}

module "lambda_alias" {
  source  = "terraform-aws-modules/lambda/aws//modules/alias"
  version = "~> 7.0"

  depends_on = [
    module.lambda,
    module.sqs_queue_for_lambda
  ]

  refresh_alias    = false
  name             = var.lambda.alias_name
  function_name    = module.lambda.lambda_function_arn
  function_version = module.lambda.lambda_function_version

  event_source_mapping = {
    sqs = {
      event_source_arn                   = module.sqs_queue_for_lambda.queue_arn
      maximum_batching_window_in_seconds = var.lambda.sqs_mapping.maximum_batching_window_in_seconds
      batch_size                         = var.lambda.sqs_mapping.batch_size
      enabled                            = true
    }
  }
}

module "deploy" {
  source  = "terraform-aws-modules/lambda/aws//modules/deploy"
  version = "~> 7.0"

  depends_on = [
    module.lambda,
    module.lambda_alias
  ]

  alias_name    = module.lambda_alias.lambda_alias_name
  function_name = module.lambda.lambda_function_name

  target_version = module.lambda.lambda_function_version
  description    = "${var.lambda.name} lambda deployment!"

  create_app = true
  app_name   = "${var.lambda.name}-app"

  create_deployment_group = true
  deployment_group_name   = "${var.lambda.name}-deployment-group"

  create_deployment          = true
  run_deployment             = true
  deployment_config_name     = var.lambda.deployment_config_name
  save_deploy_script         = false
  wait_deployment_completion = true
  force_deploy               = false
}

# ----------------------------------------
# DataDog subscription to CloudWatch Logs
# ----------------------------------------
data "aws_ssm_parameter" "datadog_kinesis_log_arn" {
  name = "ddog_kinesis_log_stream_arn"
}

data "aws_ssm_parameter" "datadog_kinesis_log_iam_role_arn" {
  name = "ddog_kinesis_log_stream_iam_role_arn"
}

resource "aws_cloudwatch_log_subscription_filter" "cloudwatch_log_events" {
  depends_on = [module.lambda]

  name            = var.lambda.name
  role_arn        = data.aws_ssm_parameter.datadog_kinesis_log_iam_role_arn.value
  log_group_name  = module.lambda.lambda_cloudwatch_log_group_name
  filter_pattern  = ""
  destination_arn = data.aws_ssm_parameter.datadog_kinesis_log_arn.value
}
