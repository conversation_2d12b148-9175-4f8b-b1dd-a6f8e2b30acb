import logging
from datetime import date
from typing import override

from src.abc.event_handler import <PERSON><PERSON><PERSON><PERSON>
from src.events.accom_class_mapping_changed_event import AccomClassMappingChangedEvent
from src.log_config.setup import setup_logger
from src.service.data_refresh_service import DataRefreshServiceFactory
from src.service.dynamodb_service import DynamoDBService
from src.service.g3_any_api_service import G3AnyApiService
from src.service.g3_data_pull_service import G3DataPullServiceFactory
from src.service.integration_setting_service import IntegrationSettingService
from src.service.s3_service import S3Service
from src.service.sns_service import SNSService


class AccomTypeAccomClassMappingChange(EventHandler):

    @override
    def handle(self, event: AccomClassMappingChangedEvent) -> None:
        assert isinstance(event, AccomClassMappingChangedEvent), "event is not AccomClassMappingChanged"
        logger = logging.getLogger(__name__)
        setting_service: IntegrationSettingService = IntegrationSettingService.from_int_set_url()
        if not setting_service.is_dynamic_optimization_enabled(event.client_code, event.property_code):
            logger.debug("Skipping ACCOMODATION_CONFIG_CHANGE event")
            return
        logger.info("Handling ACCOMODATION_CONFIG_CHANGEd event")

        client_code = event.client_code
        property_code = event.property_code
        caught_up_date = event.caught_up_date
        g3_data_pull_service = G3DataPullServiceFactory.init(event.base_callback_u_r_l,
                                                             client_code, property_code)
        accom_types = g3_data_pull_service.fetch_accom_types()
        db_service = DynamoDBService()
        db_service.update_accom_class_mapping(accom_class_mapping=accom_types, client_code=client_code, property_code=property_code)

        data_refresh_service = DataRefreshServiceFactory.from_services(g3_data_pull_service, dynamodb_service=db_service,
                                                                       s3_service=S3Service(), sns_service=SNSService(), g3_any_api_service=G3AnyApiService(g3_data_pull_service.g3, event.property_id))

        data_refresh_service.refresh_calibration_data(client_code=client_code, property_code=property_code,
                                                      caught_up_date=caught_up_date, property_id=event.property_id,
                                                      g3_base_call_url=event.base_callback_u_r_l)



if __name__ == '__main__':
    setup_logger()
    AccomTypeAccomClassMappingChange().handle(AccomClassMappingChangedEvent(base_callback_u_r_l="https://g3.ideas.com",
                                                   client_code='Hilton', property_code='RMGCH', property_id=993844,
                                                   caught_up_date=date.fromisoformat('2024-11-18')))