resource "aws_sns_topic_subscription" "uen_dyn_opt_data_collector_subscription" {
  topic_arn     = var.fds_uen_arn
  protocol      = "sqs"
  endpoint      = module.data_collector.sqs_arn
  filter_policy = <<EOF
{
  "sourceSystem": ["g3", "ans", "dyn-opt"],
  "eventType": [
    "ACCOMODATION_CONFIG_CHANGE",
    "FT_AC_REPORT_GENERATED",
    "PotentialCalibrationEvent",
    "BDE_COMPLETED",
    "SIGNIFICANT_SOLDS_DRIFT_DETECTED",
    "FORECAST_GROUP_CONFIG_CHANGED",
    "IDP_COMPLETED",
    "IDP_TRIGGERED"
  ]
}
EOF
}


resource "aws_sqs_queue_policy" "allow_uen_to_send_data_collector_events" {
  queue_url = module.data_collector.sqs_url
  policy    = <<EOF
{
  "Version": "2012-10-17",
  "Id": "sqspolicy",
  "Statement": [
    {
      "Sid": "Allow unified notification sns send to ups client create sqs",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "sqs:SendMessage",
      "Resource": "${module.data_collector.sqs_arn}",
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "${var.fds_uen_arn}"
        }
      }
    }
  ]
}
EOF
}

##### SOLDS DRIFT SNS SUBSCRIPTION ######


resource "aws_sns_topic_subscription" "uen_solds_drift_subscription" {
  topic_arn     = var.fds_uen_arn
  protocol      = "sqs"
  endpoint      = module.solds_drift_handler.sqs_arn
  filter_policy = <<EOF
{
  "sourceSystem": ["integrations.pmsinbound"],
  "eventType": [
    "SOLDS_DRIFT_DETECTED"
  ]
}
EOF
}


resource "aws_sqs_queue_policy" "allow_uen_to_send_solds_drift_events" {
  queue_url = module.solds_drift_handler.sqs_url
  policy    = <<EOF
{
  "Version": "2012-10-17",
  "Id": "sqspolicy",
  "Statement": [
    {
      "Sid": "Allow unified notification sns send to ups client create sqs",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "sqs:SendMessage",
      "Resource": "${module.solds_drift_handler.sqs_arn}",
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "${var.fds_uen_arn}"
        }
      }
    }
  ]
}
EOF
}


###
