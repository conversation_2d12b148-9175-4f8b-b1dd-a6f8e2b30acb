from datetime import date, datetime, timed<PERSON>ta
from decimal import Decimal
from typing import Dict

import pandas as pd
from awswrangler import dynamodb
import awswrangler as wr
from boto3.dynamodb.conditions import Key, Attr
from pyarrow import decimal128, string as pa_string, schema

from src.dto.accom_type import AccomType
from src.dto.delta_lrv_evaluation import DeltaLRVEvaluation
from src.dto.idp_count import IdpCount
from src.dto.inc_solds import IncrementalSolds
from src.enums.env_var import EnvironmentVariable
from src.util.env_service import get_value
from src.util.pydantic_util import to_dict_array, from_dict_array


class DynamoDBService:
    DEFAULT_DATETIME = datetime.fromisoformat('1970-01-01')
    LAST_OPTIMIZATION_TIME = 'lastOptimizationTime'
    LAST_DOP_EVAL_TIME = 'lastDOPEvaluationTime'
    BATCH_SIZE = 100
    DIGIT_4_PRECISION = decimal128(4, 0)
    INS_SOLDS_PYARROW_SCHEMA = schema(
        [("clientCode_propertyCode", pa_string()), ("accomClassId_occupancyDate", pa_string()),
         ("accomClassId", DIGIT_4_PRECISION), ("clientCode", pa_string()),
         ("propertyCode", pa_string()), ("lastUpdatedTime", pa_string()),
         ("incSolds", DIGIT_4_PRECISION), ("occupancyDate", pa_string())])

    RETENTION_PERIOD = {
        'dyn-opt-decision-change': 30,
        'dyn-opt-idp-count': 30,
        'dyn-opt-idp-window': 30,
        'dyn-opt-idp-window-by-roomclass': 30
    }

    def update_accom_class_mapping(self, accom_class_mapping: list[AccomType], client_code: str, property_code: str):
        table_name = get_value(EnvironmentVariable.ACCOM_TYPE_MAPPING_DB, 'do_table_name')
        items = [a.dynamodb_record(client_code, property_code) for a in accom_class_mapping]
        dynamodb.put_items(items=items, table_name=table_name)

    def fetch_accom_type_mapping(self, client_code: str, property_code: str) -> dict:
        table_name = get_value(EnvironmentVariable.ACCOM_TYPE_MAPPING_DB, 'do_table_name')
        items = dynamodb.read_items(table_name,
                                    key_condition_expression=(
                                        Key('clientCode_propertyCode').eq(
                                            AccomType.get_partition_key(client_code, property_code))),
                                    as_dataframe=False)
        return {a['accomType']: a['accomClassId'] for a in items}

    def fetch_potential_calib_date(self, client_code: str, property_code: str) -> date:
        return self.fetch_calib_date_by_name('potentialCalibration', client_code, property_code)

    def fetch_lrv_decision(self, client_code: str, property_code: str):
        return dynamodb.read_items('dyn-opt-delta-lrv-latest',
                                       key_condition_expression=(
                                           Key('clientCode_propertyCode').eq(
                                               AccomType.get_partition_key(client_code, property_code))))

    def fetch_price_decision(self, client_code: str, property_code: str):
        return dynamodb.read_items('dyn-opt-ref-price-latest',
                                   key_condition_expression=(
                                       Key('clientCode_propertyCode').eq(
                                           AccomType.get_partition_key(client_code, property_code))))


    def fetch_potential_revision_date(self, client_code: str, property_code: str) -> date:
        return self.fetch_calib_date_by_name('potentialRevision', client_code, property_code)

    def fetch_calib_date_by_name(self, calib_item_name, client_code, property_code):
        jan_1st_1970 = date(1970, 1, 1)
        table_name = get_value(EnvironmentVariable.CALIB_DATE_TABLE, 'do_table_name')
        items = dynamodb.read_items(table_name,
                                    key_condition_expression=(Key('clientCode_propertyCode')
                                                              .eq(AccomType.get_partition_key(client_code,
                                                                                              property_code))) &
                                                             (Key('calibrationItem').eq(calib_item_name)),
                                    as_dataframe=False)
        return self.DEFAULT_DATETIME.date() if not items or items[0]['calibDate'] is None else \
            datetime.strptime(items[0]['calibDate'],'%Y-%m-%d').date()

    def update_incremental_solds(self, items: list[IncrementalSolds]):
        table_name = get_value(EnvironmentVariable.PROPERTY_INC_SOLDS_TABLE, 'do_table_name')
        dynamodb.put_items(items=to_dict_array(list[IncrementalSolds], items), table_name=table_name)

    def update_delta_lrv_latest(self, items, client_code: str, property_code: str):
        table_name = get_value(EnvironmentVariable.DELTA_LRV_LATEST_TABLE, 'do_table_name')
        self.delete_all_items(client_code, property_code, table_name)
        self.put_items(items, table_name, client_code, property_code)

    def update_available_capacity(self, items, client_code: str, property_code: str):
        table_name = get_value(EnvironmentVariable.AVAILABLE_CAPACITY_TABLE, 'do_table_name')
        self.delete_all_items(client_code, property_code, table_name)
        self.put_items(items, table_name, client_code, property_code)


    def get_current_lrv(self, client_code: str, property_code: str):
        table_name = get_value(EnvironmentVariable.DELTA_LRV_LATEST_TABLE, 'do_table_name')
        return dynamodb.read_items(table_name,
                                   key_condition_expression=(
                                       Key('clientCode_propertyCode').eq(
                                           DeltaLRVEvaluation.get_partition_key(client_code, property_code))))

    def update_decision_change(self, decision_change: float, client_code: str, property_code: str, caught_up_date):
        table_name = get_value(EnvironmentVariable.DECISION_CHANGE_TABLE, 'do_table_name')
        opt_time = datetime.now().isoformat()
        retention_period = self.RETENTION_PERIOD[table_name]
        dynamodb.put_items([{
                'clientCode_propertyCode': f'{client_code}_{property_code}',
                'caughtUpDate_optimizationTime': f'{caught_up_date}_{opt_time}',
                'caughtUpDate': caught_up_date.isoformat(),
                'optimizationTime': opt_time,
                'medianLrvChange': Decimal(str(decision_change)),
                'ttl' : self.get_expiry_time(caught_up_date, retention_period)
        }], table_name=table_name)

    def update_ref_price_latest(self, items, client_code: str, property_code: str):
        table_name = get_value(EnvironmentVariable.REF_PRICE_LATEST_TABLE, 'do_table_name')
        self.delete_all_items(client_code, property_code, table_name)
        self.put_items(items, table_name, client_code, property_code)

    def put_items(self, items, table_name, client_code: str, property_code: str):
        dynamodb.put_items(items=[i.dynamodb_record(client_code, property_code) for i in items], table_name=table_name)

    def delete_all_items(self, client_code, property_code, table_name):
        items_to_delete = dynamodb.read_items(table_name, key_condition_expression=(
            Key('clientCode_propertyCode').eq(DeltaLRVEvaluation.get_partition_key(client_code, property_code))),
                                              as_dataframe=False)
        dynamodb.delete_items(items_to_delete, table_name=table_name)
        return items_to_delete

    def fetch_incremental_solds(self, client_code: str, property_code: str) -> list[IncrementalSolds]:
        table_name = get_value(EnvironmentVariable.PROPERTY_INC_SOLDS_TABLE, 'do_table_name')
        items = dynamodb.read_items(table_name, key_condition_expression=(
            Key('clientCode_propertyCode').eq(IncrementalSolds.get_partition_key(client_code, property_code))),
                                    as_dataframe=False,
                                    pyarrow_additional_kwargs={'schema': self.INS_SOLDS_PYARROW_SCHEMA})
        return from_dict_array(list[IncrementalSolds], items)

    def truncate_inc_solds(self, client_code: str, property_code: str):
        table_name = get_value(EnvironmentVariable.PROPERTY_INC_SOLDS_TABLE, 'do_table_name')
        return self.delete_all_items(client_code, property_code, table_name)

    def increment_idp_count(self, client_code: str, property_code: str, caught_up_date: date):
        table_name = get_value(EnvironmentVariable.IDP_COUNT_TABLE, 'do_table_name')
        partition_key = IdpCount.get_partition_key(client_code, property_code)
        new_count = self.fetch_idp_count(client_code, property_code, caught_up_date) + 1
        retention_period = self.RETENTION_PERIOD[table_name]

        updated_item = [{
                'clientCode_propertyCode': partition_key,
                'count': new_count,
                'caughtUpDate': caught_up_date.isoformat(),
                'ttl' : self.get_expiry_time(caught_up_date.isoformat(), retention_period)
            }]
        dynamodb.put_items(table_name=table_name, items=updated_item)

    def fetch_idp_count(self, client_code: str, property_code: str, caught_up_date: date) -> int:
        table_name = get_value(EnvironmentVariable.IDP_COUNT_TABLE, 'do_table_name')
        partition_key = IdpCount.get_partition_key(client_code, property_code)

        items = dynamodb.read_items(table_name,
                                    partition_values=[partition_key],
                                    sort_values=[caught_up_date.isoformat()],
                                    as_dataframe=False)
        return 0 if len(items[0]) <= 0 else int(items[0]['count'])

    def reset_idp_count(self, client_code: str, property_code: str):
        table_name = get_value(EnvironmentVariable.IDP_COUNT_TABLE, 'do_table_name')
        self.delete_all_items(client_code, property_code, table_name)

    def save_idp_window(self, client_code: str, property_code: str, evaluation_time: datetime, caught_up_date: date,
                        max_occ_date: date):
        table_name = get_value(EnvironmentVariable.IDP_WINDOW_TABLE, 'do_table_name')
        retention_period = self.RETENTION_PERIOD[table_name]
        items = [{
                'clientCode_propertyCode': IdpCount.get_partition_key(client_code, property_code),
                'evaluationTime': evaluation_time.isoformat(),
                'caughtUpDate': caught_up_date.isoformat(),
                'caughtUpDate_evaluationTime': f'{caught_up_date.isoformat()}_{evaluation_time.isoformat()}',
                'maxOccupancyDate': max_occ_date.isoformat(),
                'ttl' : self.get_expiry_time(caught_up_date.isoformat(), retention_period)
            }]
        dynamodb.put_items(items, table_name)

    def save_idp_window_by_roomclass(self, client_code: str, property_code: str, evaluation_time: datetime, caught_up_date: date, rc_to_occupancy_date_mapping: Dict[str, str]):
        table_name = get_value(EnvironmentVariable.IDP_WINDOW_BY_ROOMCLASS_TABLE, 'do_table_name')
        retention_period = self.RETENTION_PERIOD[table_name]
        to_save = [ {
            'clientCode_propertyCode': IdpCount.get_partition_key(client_code, property_code),
            'caughtUpDate_evaluationTime_accomClassId': f'{caught_up_date.isoformat()}_{evaluation_time.isoformat()}_{rc}',
            'evaluationTime': evaluation_time.isoformat(),
            'caughtUpDate': caught_up_date.isoformat(),
            'accomClassId': int(rc),
            'maxOccupancyDate': datetime.strptime(max_occ_date, "%Y-%m-%d").date().isoformat(),
            'ttl' : self.get_expiry_time(caught_up_date.isoformat(), retention_period),
        } for rc, max_occ_date in rc_to_occupancy_date_mapping.items()]
        dynamodb.put_items(items=to_save, table_name=table_name)

    def fetch_all_incremental_solds(self):
        table_name = get_value(EnvironmentVariable.PROPERTY_INC_SOLDS_TABLE, 'do_table_name')
        return dynamodb.read_items(allow_full_scan=True, table_name=table_name,
                                   filter_expression=Attr('incSolds').ne(Decimal('0')),
                                   pyarrow_additional_kwargs={'schema': self.INS_SOLDS_PYARROW_SCHEMA})

    def update_property_last_incremental_time(self, client_code: str, property_code: str):
        self.truncate_property_last_incremental_time(client_code, property_code)
        table_name = get_value(EnvironmentVariable.LAST_INCREMENTAL_TIME_TABLE, 'do_table_name')
        items = [{
            'clientCode': client_code,
            'propertyCode': property_code,
            'processedDttm': datetime.now().isoformat()
        }]
        dynamodb.put_items(items=items, table_name=table_name)

    def truncate_property_last_incremental_time(self, client_code: str, property_code: str):
        table_name = get_value(EnvironmentVariable.LAST_INCREMENTAL_TIME_TABLE, 'do_table_name')
        items_to_delete = dynamodb.read_items(table_name, key_condition_expression=(
                Key('clientCode').eq(client_code) & Key('propertyCode').eq(property_code)), as_dataframe=False)
        dynamodb.delete_items(items_to_delete, table_name=table_name)
        return items_to_delete

    def fetch_all_last_incremental_time_properties(self) -> pd.DataFrame:
        table_name = get_value(EnvironmentVariable.LAST_INCREMENTAL_TIME_TABLE, 'do_table_name')
        items = dynamodb.read_items(allow_full_scan=True, table_name=table_name, as_dataframe=True)
        return items[['clientCode', 'propertyCode']] if not items.empty else pd.DataFrame()

    def get_expiry_time(self, caught_up_date, retention_period):
        if isinstance(caught_up_date, (datetime, date)):
            caught_up_date = caught_up_date.strftime("%Y-%m-%d")
        caught_up_date = datetime.strptime(caught_up_date, "%Y-%m-%d")
        ttl_datetime = caught_up_date + timedelta(days=retention_period)
        ttl_epoch = int(ttl_datetime.timestamp())
        return ttl_epoch

    #Will be executed once, will be deprecated later
    def update_items_with_ttl(self):
        RETENTION_PERIOD = {
            'dyn-opt-decision-change': 30,
            'dyn-opt-idp-count': 30,
            'dyn-opt-idp-window': 30,
            'dyn-opt-idp-window-by-roomclass': 30
        }

        for table, retention_period in RETENTION_PERIOD.items():
            self.apply_ttl_to_table(
                table_name=table,
                retention_period= retention_period
            )

    def apply_ttl_to_table(self, table_name: str, retention_period: int):
        items = dynamodb.read_items(allow_full_scan=True, table_name=table_name, as_dataframe=False)
        updated_items = []

        for item in items:
            caught_up_str = item.get('caughtUpDate')

            caught_up_date = datetime.strptime(caught_up_str, "%Y-%m-%d")
            ttl_datetime = caught_up_date + timedelta(days=retention_period)
            ttl_epoch = int(ttl_datetime.timestamp())
            item['ttl'] = ttl_epoch
            updated_items.append(item)

        if updated_items:
            dynamodb.put_items(items=updated_items, table_name=table_name)

    def fetch_calib_date_for_input_properties(self, df: pd.DataFrame, calibrationItem: str) -> pd.DataFrame:
        table_name = get_value(EnvironmentVariable.CALIB_DATE_TABLE, 'do_table_name')
        df = df[['clientCode', 'propertyCode']]
        df['clientCode_propertyCode'] = df['clientCode'] + '_' + df['propertyCode']
        df['calibrationItem'] = calibrationItem
        items = self.batch_read_items(
            table_name,
            df['clientCode_propertyCode'].tolist(),
            df['calibrationItem'].tolist(),
            self.BATCH_SIZE
        )
        if items.empty:
            return pd.DataFrame(columns=['clientCode','propertyCode','calibrationItem', 'calibDate'])
        items[['clientCode', 'propertyCode']] = items['clientCode_propertyCode'].str.split('_', expand=True)
        items = df.merge(items, on=['clientCode', 'propertyCode', 'clientCode_propertyCode', 'calibrationItem'], how='left')
        items = items.fillna(self.DEFAULT_DATETIME.isoformat())
        return items[['clientCode', 'propertyCode', 'calibrationItem', 'calibDate']]

    def batch_read_items(self, table_name, partition_values, sort_values, batch_size=100):
        items = []
        for i in range(0, len(partition_values), batch_size):
            batch_partition_values = partition_values[i:i + batch_size]
            batch_sort_values = sort_values[i:i + batch_size]
            batch_items = dynamodb.read_items(
                table_name=table_name,
                partition_values=batch_partition_values,
                sort_values=batch_sort_values,
                as_dataframe=True
            )

            items.append(batch_items)
        return pd.concat(items, ignore_index=True)


    def update_last_optimization_time(self, client_code: str, property_code: str):
        table_name = get_value(EnvironmentVariable.CALIB_DATE_TABLE, 'do_table_name')
        value = [{
            'clientCode_propertyCode': f'{client_code}_{property_code}',
            'calibrationItem': self.LAST_OPTIMIZATION_TIME,
            'calibDate': datetime.now().isoformat()
        }]
        dynamodb.put_items(items=value, table_name=table_name)

    def update_last_dop_evaluation_time(self, client_code: str, property_code: str, evaluation_time: datetime):
        table_name = get_value(EnvironmentVariable.CALIB_DATE_TABLE, 'do_table_name')
        value = [{
            'clientCode_propertyCode': f'{client_code}_{property_code}',
            'calibrationItem': self.LAST_DOP_EVAL_TIME,
            'calibDate': evaluation_time.isoformat()
        }]
        dynamodb.put_items(items=value, table_name=table_name)

    def fetch_idp_window(self, *, client_code, property_code, caught_up_dates=None):
        filter_expression = None
        if caught_up_dates is not None and len(caught_up_dates) > 0:
            filter_expression = Attr('caughtUpDate').is_in(caught_up_dates)
        df = dynamodb.read_items(table_name=get_value(EnvironmentVariable.IDP_WINDOW_TABLE, 'do_table_name'),
                                 key_condition_expression=Key('clientCode_propertyCode').eq(
                                     fr'{client_code}_{property_code}'),
                                 filter_expression=filter_expression)
        if df.empty:
            return pd.DataFrame(columns=['clientCode', 'propertyCode', 'evaluationTime', 'caughtUpDate', 'maxOccupancyDate'])
        df[['clientCode', 'propertyCode']] = df['clientCode_propertyCode'].str.split('_', expand=True)
        return df[['clientCode', 'propertyCode', 'evaluationTime', 'caughtUpDate', 'maxOccupancyDate']]


dynamodb_service = DynamoDBService()

if __name__ == '__main__':
    dynamodb_service = DynamoDBService()
    dynamodb_service.update_items_with_ttl()
    # TODAY = datetime(2025,6,4)
    # dynamodb_service.save_idp_window_by_roomclass(
    #     client_code='Hilton',
    #     property_code='REKCU',
    #     evaluation_time=TODAY,
    #     caught_up_date=TODAY.date(),
    #     rc_to_occupancy_date_mapping={
    #         '7': '2025-03-04',
    #         '8': '2025-03-05'
    #     }
    # )

