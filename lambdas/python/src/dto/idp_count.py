from datetime import date

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel


class IdpCount(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    client_code: str
    property_code: str
    count: int
    caught_up_date: date

    @staticmethod
    def get_partition_key(client_code: str, property_code: str):
        return f'{client_code}_{property_code}'
