from unittest.mock import patch, MagicMock, call
from datetime import datetime, date

from src.dto.inc_solds import IncrementalSolds
from src.events.solds_drift_detected_event import SoldsDriftDetectedEvent, Drift
from src.handler.solds_drift_handler import SoldsDriftHandler


class TestSoldsDriftHandler:
    INC_SOLD = IncrementalSolds(property_code='p', client_code='c', accom_class_id=1, inc_solds=10,
                                occupancy_date=str(date.today()),
                                last_updated_time=str(datetime.now()))

    EVENT = SoldsDriftDetectedEvent(property_code='p', client_code='c',
                                    drift=Drift(incrementalSoldsByOccupancyDate={date.today(): 1},
                                                market_segment='1',
                                                rate_code='1',
                                                reservation_id='1',
                                                room_type_code='NKRUD'),
                                    blocked=False)

    @patch('src.handler.solds_drift_handler.DynamoDBService.fetch_accom_type_mapping')
    @patch('src.handler.solds_drift_handler.DynamoDBService.fetch_incremental_solds')
    @patch('src.handler.solds_drift_handler.DynamoDBService.update_incremental_solds')
    @patch('src.handler.solds_drift_handler.DynamoDBService.update_property_last_incremental_time')
    def test_handle_when_drift_for_existing_property(self, update_property_last_incremental_time: MagicMock,
                                                     update_incremental_solds: MagicMock,
                                                     fetch_incremental_solds, fetch_accom_type_mapping) -> None:
        fetch_accom_type_mapping.return_value = {'NKRUD': 1}
        fetch_incremental_solds.return_value = [self.INC_SOLD]
        update_incremental_solds.return_value = None
        update_property_last_incremental_time.return_value = None

        handler = SoldsDriftHandler()

        handler.handle(self.EVENT)

        assert fetch_accom_type_mapping.call_args_list == [call('c', 'p')]
        assert fetch_incremental_solds.call_args_list == [call('c', 'p')]
        assert update_incremental_solds.call_args_list == [
            call([IncrementalSolds(property_code='p', client_code='c',
                                   accom_class_id=1,
                                   occupancy_date=str(date.today()), inc_solds=11,
                                   last_updated_time=str(datetime.now()))])]

        assert update_incremental_solds.call_args_list[0].args[0][0].inc_solds == 11
        update_property_last_incremental_time.assert_called_once_with('c', 'p')

    @patch('src.handler.solds_drift_handler.DynamoDBService.fetch_accom_type_mapping')
    @patch('src.handler.solds_drift_handler.DynamoDBService.fetch_incremental_solds')
    @patch('src.handler.solds_drift_handler.DynamoDBService.update_incremental_solds')
    @patch('src.handler.solds_drift_handler.DynamoDBService.update_property_last_incremental_time')
    def test_handle_when_drift_for_non_existing_property(self, update_property_last_incremental_time: MagicMock,
                                                         update_incremental_solds: MagicMock,
                                                         fetch_incremental_solds, fetch_accom_type_mapping):
        fetch_accom_type_mapping.return_value = {'NKRUD': 1}
        fetch_incremental_solds.return_value = []
        update_incremental_solds.return_value = None
        update_property_last_incremental_time.return_value = None

        handler = SoldsDriftHandler()

        handler.handle(self.EVENT)

        assert fetch_accom_type_mapping.call_args_list == [call('c', 'p')]
        assert fetch_incremental_solds.call_args_list == [call('c', 'p')]
        assert update_incremental_solds.call_args_list[0].args[0][0].inc_solds == 1
        update_property_last_incremental_time.assert_called_once_with('c', 'p')
