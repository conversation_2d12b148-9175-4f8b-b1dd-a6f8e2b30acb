module "data_collector" {
  source = "./modules/lambda_sqs_module"

  lambda = {
    name        = "${var.project_short_name}_data_collector"
    description = "SQS Driven Lambda to process events sent by g3."
    path        = "src/sqs_trigger.lambda_handler"
    handler     = "src/sqs_trigger.lambda_handler"
    artifact    = "../external_artifact/python.zip"
    runtime     = "python3.12"
    memory_size = 4096
    layers = [
      "arn:aws:lambda:us-east-2:************:layer:AWSSDKPandas-Python312:3",
      "arn:aws:lambda:us-east-2:017000801446:layer:AWSLambdaPowertoolsPythonV3-python312-x86_64:3"
    ]
    timeout = 600
    environment_variables = {
      ENV                           = var.environment
      S3_NAME                       = module.dyn_opt_data_s3.s3_bucket_id
      FDS_SNS_TOPIC_ARN             = var.fds_uen_arn
      G3_AUTH_SECRET_NAME           = var.secret_manager_name
      DEFAULT_PAGE_SIZE             = var.default_page_size_g3
      ACCOM_TYPE_MAPPING_DB         = module.dyn-opt-accom-type-mapping.dynamodb_table_id
      PROPERTY_INC_SOLDS_TABLE      = module.dyn_opt_property_incremental_solds.dynamodb_table_id
      LOG_LEVEL                     = "DEBUG"
      DELTA_LRV_LATEST_TABLE        = module.dyn_opt_delta_lrv_latest.dynamodb_table_id
      REF_PRICE_LATEST_TABLE        = module.dyn_opt_ref_price_latest.dynamodb_table_id
      CALIBRATED_POTENTIAL_TABLE    = module.dyn_opt_calibrated_potentials.dynamodb_table_id
      IDP_COUNT_TABLE               = module.dyn_opt_idp_count.dynamodb_table_id
      IDP_WINDOW_TABLE              = module.dyn_opt_idp_window.dynamodb_table_id
      IDP_WINDOW_BY_ROOMCLASS_TABLE = module.dyn_opt_idp_window_by_roomclass.dynamodb_table_id
      INTEGRATION_SETTING_URL       = var.integration_setting_url
      CALIB_DATE_TABLE              = module.dyn_opt_calib_date.dynamodb_table_id
      LAST_INCREMENTAL_TIME_TABLE   = module.dyn_opt_property_last_incremental_time.dynamodb_table_id,
      DECISION_CHANGE_TABLE         = module.dyn_opt_decision_change.dynamodb_table_id
      AVAILABLE_CAPACITY_TABLE      = module.dyn_opt_available_capacity.dynamodb_table_id
      UCS_BASE_URL                  = var.ucs_base_url
      UIS_BASE_URL                  = var.uis_base_url
      G3_BASE_URL                   = var.g3_base_url
    }
    custom_policies = [
      "arn:aws:iam::aws:policy/AmazonS3FullAccess",
      aws_iam_policy.secret_value_access.arn,
      "arn:aws:iam::${local.account_id}:policy/secret-value-access-policy",
      "arn:aws:iam::aws:policy/AmazonSNSFullAccess",
      aws_iam_policy.dynamodb_table_access.arn
    ]
    vpc = true
    vpc_subnet_ids = [
      data.aws_subnet.private_us_east_2a.id,
      data.aws_subnet.private_us_east_2b.id,
      data.aws_subnet.private_us_east_2c.id
    ]
    vpc_security_group_ids = [aws_security_group.lambda_can_access_the_internet.id]
  }

  sqs = {
    queue_policy_statements = {
      fds_uen_topic = {
        actions = [
          "sqs:SendMessage",
          "sqs:ReceiveMessage"
        ]
        condition = {
          test     = "ArnEquals"
          variable = "aws:SourceArn"
          values   = [var.fds_uen_arn]
        }
      }
    }
  }
}



module "solds_drift_handler" {
  source = "./modules/lambda_sqs_module"

  lambda = {
    name        = "${var.project_short_name}_solds_drift_handler"
    description = "SQS Driven Lambda to calculate inc solds send by pms inbound."
    path        = "src/sqs_trigger.lambda_handler"
    handler     = "src/sqs_trigger.lambda_handler"
    artifact    = "../external_artifact/python.zip"
    runtime     = "python3.12"
    layers = [
      "arn:aws:lambda:us-east-2:************:layer:AWSSDKPandas-Python312:3",
      "arn:aws:lambda:us-east-2:017000801446:layer:AWSLambdaPowertoolsPythonV3-python312-x86_64:3"
    ]
    timeout = 600
    environment_variables = {
      ENV                           = var.environment
      S3_NAME                       = module.dyn_opt_data_s3.s3_bucket_id
      FDS_SNS_TOPIC_ARN             = var.fds_uen_arn
      G3_AUTH_SECRET_NAME           = var.secret_manager_name
      DEFAULT_PAGE_SIZE             = var.default_page_size_g3
      ACCOM_TYPE_MAPPING_DB         = module.dyn-opt-accom-type-mapping.dynamodb_table_id
      PROPERTY_INC_SOLDS_TABLE      = module.dyn_opt_property_incremental_solds.dynamodb_table_id
      LOG_LEVEL                     = "DEBUG"
      DELTA_LRV_LATEST_TABLE        = module.dyn_opt_delta_lrv_latest.dynamodb_table_id
      REF_PRICE_LATEST_TABLE        = module.dyn_opt_ref_price_latest.dynamodb_table_id
      CALIBRATED_POTENTIAL_TABLE    = module.dyn_opt_calibrated_potentials.dynamodb_table_id
      IDP_COUNT_TABLE               = module.dyn_opt_idp_count.dynamodb_table_id
      IDP_WINDOW_TABLE              = module.dyn_opt_idp_window.dynamodb_table_id
      IDP_WINDOW_BY_ROOMCLASS_TABLE = module.dyn_opt_idp_window_by_roomclass.dynamodb_table_id
      INTEGRATION_SETTING_URL       = var.integration_setting_url
      LAST_INCREMENTAL_TIME_TABLE   = module.dyn_opt_property_last_incremental_time.dynamodb_table_id
      CALIB_DATE_TABLE              = module.dyn_opt_calib_date.dynamodb_table_id,
      DECISION_CHANGE_TABLE         = module.dyn_opt_decision_change.dynamodb_table_id
      AVAILABLE_CAPACITY_TABLE      = module.dyn_opt_available_capacity.dynamodb_table_id
      UCS_BASE_URL                  = var.ucs_base_url
      UIS_BASE_URL                  = var.uis_base_url
      G3_BASE_URL                   = var.g3_base_url
    }
    custom_policies = [
      "arn:aws:iam::aws:policy/AmazonS3FullAccess",
      aws_iam_policy.secret_value_access.arn,
      "arn:aws:iam::${local.account_id}:policy/secret-value-access-policy",
      "arn:aws:iam::aws:policy/AmazonSNSFullAccess",
      aws_iam_policy.dynamodb_table_access.arn
    ]
    vpc = false
  }

  sqs = {
    queue_policy_statements = {
      fds_uen_topic = {
        actions = [
          "sqs:SendMessage",
          "sqs:ReceiveMessage"
        ]
        condition = {
          test     = "ArnEquals"
          variable = "aws:SourceArn"
          values   = [var.fds_uen_arn]
        }
      }
    }
  }
}


module "evaluation_check" {
  source = "./modules/lambda_sqs_module"

  lambda = {
    name        = "${var.project_short_name}_evaluation_check"
    description = "SQS Driven Lambda to calculate inc solds send by pms inbound."
    path        = "src/scheduler_trigger.lambda_handler"
    handler     = "src/scheduler_trigger.lambda_handler"
    artifact    = "../external_artifact/python.zip"
    runtime     = "python3.12"
    layers = [
      "arn:aws:lambda:us-east-2:************:layer:AWSSDKPandas-Python312:3",
      "arn:aws:lambda:us-east-2:017000801446:layer:AWSLambdaPowertoolsPythonV3-python312-x86_64:3"
    ]
    timeout = 600
    environment_variables = {
      ENV                         = var.environment
      S3_NAME                     = module.dyn_opt_data_s3.s3_bucket_id
      FDS_SNS_TOPIC_ARN           = var.fds_uen_arn
      G3_AUTH_SECRET_NAME         = var.secret_manager_name
      DEFAULT_PAGE_SIZE           = var.default_page_size_g3
      ACCOM_TYPE_MAPPING_DB       = module.dyn-opt-accom-type-mapping.dynamodb_table_id
      PROPERTY_INC_SOLDS_TABLE    = module.dyn_opt_property_incremental_solds.dynamodb_table_id
      LOG_LEVEL                   = "DEBUG"
      DELTA_LRV_LATEST_TABLE      = module.dyn_opt_delta_lrv_latest.dynamodb_table_id
      REF_PRICE_LATEST_TABLE      = module.dyn_opt_ref_price_latest.dynamodb_table_id
      CALIBRATED_POTENTIAL_TABLE  = module.dyn_opt_calibrated_potentials.dynamodb_table_id
      INTEGRATION_SETTING_URL     = var.integration_setting_url
      LAST_INCREMENTAL_TIME_TABLE = module.dyn_opt_property_last_incremental_time.dynamodb_table_id
      CALIB_DATE_TABLE            = module.dyn_opt_calib_date.dynamodb_table_id,
      DECISION_CHANGE_TABLE       = module.dyn_opt_decision_change.dynamodb_table_id
      AVAILABLE_CAPACITY_TABLE    = module.dyn_opt_available_capacity.dynamodb_table_id
      UCS_BASE_URL                = var.ucs_base_url
      UIS_BASE_URL                = var.uis_base_url
      G3_BASE_URL                 = var.g3_base_url
    }
    custom_policies = [
      "arn:aws:iam::aws:policy/AmazonS3FullAccess",
      aws_iam_policy.secret_value_access.arn,
      "arn:aws:iam::${local.account_id}:policy/secret-value-access-policy",
      "arn:aws:iam::aws:policy/AmazonSNSFullAccess",
      aws_iam_policy.dynamodb_table_access.arn
    ]
    vpc = false
  }

  sqs = {
    queue_policy_statements = {
      fds_uen_topic = {
        actions = [
          "sqs:SendMessage",
          "sqs:ReceiveMessage"
        ]
        condition = {
          test     = "ArnEquals"
          variable = "aws:SourceArn"
          values   = [var.fds_uen_arn]
        }
      }
    }
  }
}
